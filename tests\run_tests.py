#!/usr/bin/env python3
# coding: utf-8

"""
FILE: run_tests.py

DESCRIPTION:
    Professional test runner for face recognition system.
    Runs all unit tests with proper documentation and cleanup.

FEATURES:
    - Run all tests or specific test categories
    - Coverage reporting with HTML output
    - Verbose output with detailed test information
    - Automatic cleanup of test groups after completion
    - Professional test execution with proper error handling

USAGE:
    python tests/run_tests.py                    # Run all tests
    python tests/run_tests.py --coverage         # Run with coverage
    python tests/run_tests.py --verbose          # Verbose output
    python tests/run_tests.py --unit             # Unit tests only
    python tests/run_tests.py --integration      # Integration tests only
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path


def main():
	"""Main test runner function"""
	parser = argparse.ArgumentParser(
		description="Face Recognition Test Runner",
		formatter_class=argparse.RawDescriptionHelpFormatter,
		epilog="""
Examples:
  python tests/run_tests.py                    # Run all tests
  python tests/run_tests.py --coverage         # Run with coverage
  python tests/run_tests.py --verbose          # Verbose output
  python tests/run_tests.py --unit             # Unit tests only
  python tests/run_tests.py --integration      # Integration tests only
  python tests/run_tests.py --file tests/test_face_recognition.py  # Specific file
		"""
	)

	# Test selection options
	parser.add_argument("--unit", action="store_true", help="Run only unit tests")
	parser.add_argument("--integration", action="store_true", help="Run only integration tests")
	parser.add_argument("--coverage", action="store_true", help="Run tests with coverage report")
	parser.add_argument("--verbose", action="store_true", help="Run with verbose output")
	parser.add_argument("--file", type=str, help="Run specific test file")
	parser.add_argument("--class", dest="test_class", type=str, help="Run specific test class")
	parser.add_argument("--function", type=str, help="Run specific test function")

	args = parser.parse_args()

	# Build pytest command
	cmd = ["uv", "run", "pytest"]

	# Add verbose flag
	if args.verbose:
		cmd.append("-v")
	else:
		cmd.append("-v")  # Always use verbose for better output

	# Add test markers
	if args.unit:
		cmd.extend(["-m", "unit"])
	elif args.integration:
		cmd.extend(["-m", "integration"])
	elif args.file:
		cmd.append(args.file)
		if args.test_class:
			cmd[-1] += f"::{args.test_class}"
			if args.function:
				cmd[-1] += f"::{args.function}"
	else:
		cmd.append("tests/test_face_recognition.py")

	# Add coverage if requested
	if args.coverage:
		cmd.extend([
			"--cov=face_recognition",
			"--cov-report=html",
			"--cov-report=term-missing"
		])

	# Add additional pytest options
	cmd.extend([
		"--tb=short",  # Shorter traceback format
		"--strict-markers",  # Strict marker checking
		"--disable-warnings"  # Disable warnings for cleaner output
	])

	print("🚀 Starting Face Recognition Tests...")
	print(f"📋 Command: {' '.join(cmd)}")
	print("=" * 80)

	try:
		# Run from project root
		result = subprocess.run(cmd, cwd=Path(__file__).parent.parent)

		print("=" * 80)
		if result.returncode == 0:
			print("✅ All tests passed successfully!")
			print("🧹 Running cleanup...")

			# Run cleanup using subprocess to avoid import issues
			try:
				cleanup_cmd = [
					sys.executable, "-c",
					"import sys; sys.path.insert(0, 'tests'); from test_face_recognition import cleanup_test_groups; cleanup_test_groups()"
				]
				cleanup_result = subprocess.run(
					cleanup_cmd,
					cwd=Path(__file__).parent.parent,
					capture_output=True,
					text=True
				)

				if cleanup_result.returncode == 0:
					# Print cleanup output
					if cleanup_result.stdout:
						print(cleanup_result.stdout.strip())
					print("✅ Cleanup completed successfully!")
				else:
					print(f"⚠️ Cleanup failed: {cleanup_result.stderr}")
			except Exception as e:
				print(f"⚠️ Cleanup error: {e}")

			if args.coverage:
				print("\n📊 Coverage report generated in htmlcov/index.html")

		else:
			print(f"❌ Tests failed with exit code: {result.returncode}")

		return result.returncode

	except KeyboardInterrupt:
		print("\n⚠️ Tests interrupted by user")
		return 1
	except Exception as e:
		print(f"❌ Error running tests: {e}")
		return 1


if __name__ == "__main__":
	exit_code = main()
	sys.exit(exit_code)

"""
FILE: run_tests.py

DESCRIPTION:
    Test runner script for the face recognition system.
    Provides convenient commands to run different types of tests.

USAGE:
    python run_tests.py                    # Run all tests
    python run_tests.py --unit             # Run only unit tests
    python run_tests.py --integration      # Run only integration tests
    python run_tests.py --coverage         # Run tests with coverage report
    python run_tests.py --verbose          # Run with verbose output
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd: list[str]) -> int:
	"""Run a command and return the exit code"""
	print(f"Running: {' '.join(cmd)}")
	# Run from project root (parent of tests directory)
	result = subprocess.run(cmd, cwd=Path(__file__).parent.parent)
	return result.returncode


def main():
	"""Main test runner function"""
	parser = argparse.ArgumentParser(description="Face Recognition Test Runner")
	parser.add_argument("--unit", action="store_true", help="Run only unit tests")
	parser.add_argument("--integration", action="store_true", help="Run only integration tests")
	parser.add_argument("--coverage", action="store_true", help="Run tests with coverage report")
	parser.add_argument("--verbose", action="store_true", help="Run with verbose output")
	parser.add_argument("--file", type=str, help="Run specific test file")
	parser.add_argument("--class", dest="test_class", type=str, help="Run specific test class")
	parser.add_argument("--function", type=str, help="Run specific test function")
	
	args = parser.parse_args()
	
	# Base pytest command
	cmd = ["uv", "run", "pytest"]
	
	# Add test selection
	if args.unit:
		cmd.extend(["-m", "unit"])
	elif args.integration:
		cmd.extend(["-m", "integration"])
	elif args.file:
		cmd.append(args.file)
		if args.test_class:
			cmd[-1] += f"::{args.test_class}"
			if args.function:
				cmd[-1] += f"::{args.function}"
	else:
		cmd.append("tests/test_face_recognition.py")
	
	# Add coverage if requested
	if args.coverage:
		cmd.extend([
			"--cov=face_recognition",
			"--cov-report=html",
			"--cov-report=term-missing"
		])
	
	# Add verbose output
	if args.verbose:
		cmd.append("-vv")
	
	# Run the tests
	print("🧪 Face Recognition System Test Runner")
	print("=" * 50)
	
	exit_code = run_command(cmd)
	
	if exit_code == 0:
		print("\n✅ All tests passed!")
		if args.coverage:
			print("📊 Coverage report generated in htmlcov/")
	else:
		print(f"\n❌ Tests failed with exit code {exit_code}")
	
	return exit_code


if __name__ == "__main__":
	sys.exit(main())
