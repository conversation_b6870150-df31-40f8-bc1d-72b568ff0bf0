# coding: utf-8

"""
FILE: test_face_recognition.py

DESCRIPTION:
    Unit tests for the face recognition system covering all public methods.
    Tests every function with proper documentation and cleanup functionality.

TEST COVERAGE:
    - FaceRecognitionManager: All public methods and save_known_images parameter
    - FaceGroupManager: Frame processing and coordination
    - KnownFaceGroup: Known person management and optional image saving
    - UnknownFaceGroup: Unknown person operations and automatic image saving
    - BaseGroupManager: Azure API operations and image saving methods
    - ImageSaving: Unknown and known face image saving functionality
    - UnknownToKnownTransfer: Person transfer with image migration
    - SaveKnownImagesParameter: Configuration testing for image saving
    - UnknownImageAutoSaving: Automatic unknown face image saving
    - Data Classes: FrameProcessingResult, DetectedFace
    - Cleanup: Remove all test groups after completion

USAGE:
    pytest tests/test_face_recognition.py -v
    python tests/run_tests.py
"""

import pytest
import numpy as np
import os
import sys
import tempfile
import shutil
import json
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any
from datetime import datetime
from pathlib import Path

# Import face recognition components
from face_recognition.face_recognition_manager import FaceRecognitionManager
from face_recognition.face_group_manager import FaceGroupManager, FrameProcessingResult, DetectedFace
from face_recognition.known_face_group import KnownFaceGroup
from face_recognition.unknown_face_group import UnknownFaceGroup
from face_recognition.base_group_manager import BaseGroupManager


# Test Fixtures
@pytest.fixture
def azure_credentials():
	"""Mock Azure credentials for testing"""
	return {
		"endpoint": "https://test-face-api.cognitiveservices.azure.com/",
		"api_key": "test-api-key-12345"
	}


@pytest.fixture
def sample_image():
	"""Sample face image for testing"""
	return np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)


@pytest.fixture
def sample_frame():
	"""Sample video frame for testing"""
	return np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)


@pytest.fixture
def temp_image_dir():
	"""Create temporary directory for image saving tests"""
	temp_dir = tempfile.mkdtemp()
	yield temp_dir
	# Cleanup after test
	if os.path.exists(temp_dir):
		shutil.rmtree(temp_dir)


@pytest.fixture
def mock_face_client():
	"""Mock Azure Face client"""
	mock_client = Mock()
	mock_client.large_person_group = Mock()
	mock_client.face = Mock()

	# Mock responses
	mock_client.large_person_group.get.return_value = Mock(large_person_group_id="test_group")
	mock_client.large_person_group.create.return_value = None
	mock_client.large_person_group.create_person.return_value = Mock(person_id="person-123")
	mock_client.large_person_group.add_face.return_value = Mock(persisted_face_id="face-456")
	mock_client.large_person_group.get_persons.return_value = []
	mock_client.large_person_group.identify.return_value = []
	mock_client.face.detect.return_value = []

	return mock_client


@pytest.fixture
def temp_directory():
	"""Temporary directory for file operations"""
	temp_dir = tempfile.mkdtemp()
	yield temp_dir
	shutil.rmtree(temp_dir, ignore_errors=True)


class TestFaceRecognitionManager:
	"""Tests for FaceRecognitionManager - main public API"""

	def test_initialization_default_parameters(self, azure_credentials):
		"""Test manager initialization with default parameters"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager:
			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			assert manager.endpoint == azure_credentials["endpoint"]
			assert manager.api_key == azure_credentials["api_key"]
			assert manager.group_id == "test_group"
			assert manager.group_name == "Test Group"
			assert manager.auto_train_unknown == True
			assert manager.max_faces_per_person == 10
			assert manager.unknown_confidence_threshold == 0.8
			assert manager.face_similarity_threshold == 0.8
			mock_group_manager.assert_called_once()

	def test_initialization_custom_parameters(self, azure_credentials):
		"""Test manager initialization with custom parameters"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager'):
			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="custom_group",
				group_name="Custom Group",
				auto_train_unknown=False,
				max_faces_per_person=5,
				unknown_confidence_threshold=0.9,
				face_similarity_threshold=0.7
			)

			assert manager.auto_train_unknown == False
			assert manager.max_faces_per_person == 5
			assert manager.unknown_confidence_threshold == 0.9
			assert manager.face_similarity_threshold == 0.7

	def test_setup_logger(self, azure_credentials):
		"""Test logger setup creates proper configuration"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager'):
			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			logger = manager._setup_logger()
			assert logger.name == "FaceRecognitionManager"
			assert len(logger.handlers) > 0
			assert logger.level == 20  # INFO level

	def test_process_frame(self, azure_credentials, sample_frame):
		"""Test frame processing delegates to group manager"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager

			mock_result = FrameProcessingResult(
				detected_faces=[],
				total_faces=0,
				known_faces=0,
				unknown_faces=0,
				processing_time=0.1,
				annotated_frame=sample_frame
			)
			mock_group_manager.process_frame.return_value = mock_result

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.process_frame(sample_frame, confidence_threshold=0.7)

			mock_group_manager.process_frame.assert_called_once_with(
				sample_frame, 0.7, True, None
			)
			assert result == mock_result

	def test_create_known_person(self, azure_credentials):
		"""Test creating known person delegates correctly"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_group_manager.create_known_person.return_value = "person-123"

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.create_known_person("John Doe", "Manager")

			mock_group_manager.create_known_person.assert_called_once_with("John Doe", "Manager")
			assert result == "person-123"

	def test_add_face_to_known_person(self, azure_credentials, sample_image):
		"""Test adding face to known person"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_group_manager.add_face_to_known_person.return_value = "face-456"

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.add_face_to_known_person("person-123", sample_image, "Profile photo")

			mock_group_manager.add_face_to_known_person.assert_called_once_with(
				"person-123", sample_image, "Profile photo"
			)
			assert result == "face-456"

	def test_get_known_persons(self, azure_credentials):
		"""Test getting known persons list"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_persons = [Mock(person_id="person-1"), Mock(person_id="person-2")]
			mock_group_manager.get_known_persons.return_value = mock_persons

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.get_known_persons()

			mock_group_manager.get_known_persons.assert_called_once()
			assert result == mock_persons

	def test_update_unknown_person_with_real_name(self, azure_credentials):
		"""Test updating unknown person with real name"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_group_manager.update_unknown_person_with_real_name.return_value = True

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.update_unknown_person_with_real_name("person_1", "Alice Smith", "Developer")

			mock_group_manager.update_unknown_person_with_real_name.assert_called_once_with(
				"person_1", "Alice Smith", "Developer"
			)
			assert result == True

	def test_get_unknown_persons_list(self, azure_credentials):
		"""Test getting unknown persons list"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_persons = [{"name": "person_1", "face_count": 3}]
			mock_group_manager.get_unknown_persons_list.return_value = mock_persons

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.get_unknown_persons_list()

			mock_group_manager.get_unknown_persons_list.assert_called_once()
			assert result == mock_persons

	def test_get_group_statistics(self, azure_credentials):
		"""Test getting group statistics"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_stats = {"known_primary": {"persons": 5, "faces": 15}}
			mock_group_manager.get_group_statistics.return_value = mock_stats

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.get_group_statistics()

			mock_group_manager.get_group_statistics.assert_called_once()
			assert result == mock_stats

	def test_get_training_status(self, azure_credentials):
		"""Test getting training status"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_status = {"known_primary": "succeeded", "known_secondary": "running"}
			mock_group_manager.get_training_status.return_value = mock_status

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.get_training_status()

			mock_group_manager.get_training_status.assert_called_once()
			assert result == mock_status

	def test_get_active_known_group_id(self, azure_credentials):
		"""Test getting active known group ID"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_group_manager.get_active_known_group_id.return_value = "test_group_known_primary"

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.get_active_known_group_id()

			mock_group_manager.get_active_known_group_id.assert_called_once()
			assert result == "test_group_known_primary"

	def test_get_active_unknown_group_id(self, azure_credentials):
		"""Test getting active unknown group ID"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_group_manager.get_active_unknown_group_id.return_value = "test_group_unknown_primary"

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.get_active_unknown_group_id()

			mock_group_manager.get_active_unknown_group_id.assert_called_once()
			assert result == "test_group_unknown_primary"

	def test_cleanup_all_groups(self, azure_credentials):
		"""Test cleanup all groups"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_group_manager.cleanup_all_groups.return_value = True

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			result = manager.cleanup_all_groups()

			mock_group_manager.cleanup_all_groups.assert_called_once()
			assert result == True

	def test_save_cropped_faces(self, azure_credentials, sample_image, temp_directory):
		"""Test saving cropped faces to directory"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager'):
			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			detected_faces = [
				DetectedFace(
					face_id="face-1",
					person_id="person-1",
					tag_text="John Doe",
					confidence=0.85,
					is_known=True,
					source_group="known_primary",
					bounding_box=(10, 20, 100, 120),
					cropped_image=sample_image
				)
			]

			with patch('cv2.imwrite') as mock_imwrite:
				mock_imwrite.return_value = True
				result = manager.save_cropped_faces(detected_faces, temp_directory)

				assert len(result) == 1
				assert temp_directory in result[0]
				mock_imwrite.assert_called_once()

	def test_string_representation(self, azure_credentials):
		"""Test string representation methods"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager'):
			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_group",
				group_name="Test Group"
			)

			str_repr = str(manager)
			assert "FaceRecognitionManager" in str_repr
			assert "test_group" in str_repr
			assert "Test Group" in str_repr

			repr_str = repr(manager)
			assert "FaceRecognitionManager" in repr_str
			assert "test_group" in repr_str
			assert "auto_train=True" in repr_str
			assert "max_faces=10" in repr_str


class TestFrameProcessingResult:
	"""Tests for FrameProcessingResult data class"""

	def test_frame_processing_result_creation(self, sample_frame):
		"""Test creating FrameProcessingResult with detected faces"""
		detected_face = DetectedFace(
			face_id="face-123",
			person_id="person-456",
			tag_text="John Doe",
			confidence=0.85,
			is_known=True,
			source_group="known_primary",
			bounding_box=(10, 20, 100, 120),
			cropped_image=None
		)

		result = FrameProcessingResult(
			detected_faces=[detected_face],
			total_faces=1,
			known_faces=1,
			unknown_faces=0,
			processing_time=0.15,
			annotated_frame=sample_frame
		)

		assert len(result.detected_faces) == 1
		assert result.total_faces == 1
		assert result.known_faces == 1
		assert result.unknown_faces == 0
		assert result.processing_time == 0.15
		assert result.annotated_frame is not None

	def test_frame_processing_result_empty(self, sample_frame):
		"""Test creating empty FrameProcessingResult"""
		result = FrameProcessingResult(
			detected_faces=[],
			total_faces=0,
			known_faces=0,
			unknown_faces=0,
			processing_time=0.05,
			annotated_frame=sample_frame
		)

		assert len(result.detected_faces) == 0
		assert result.total_faces == 0
		assert result.known_faces == 0
		assert result.unknown_faces == 0
		assert result.processing_time == 0.05


class TestDetectedFace:
	"""Tests for DetectedFace data class"""

	def test_detected_face_creation_known(self, sample_image):
		"""Test creating DetectedFace for known person"""
		face = DetectedFace(
			face_id="face-789",
			person_id="person-123",
			tag_text="Jane Smith (Manager)",
			confidence=0.92,
			is_known=True,
			source_group="known_primary",
			bounding_box=(50, 60, 150, 180),
			cropped_image=sample_image
		)

		assert face.face_id == "face-789"
		assert face.person_id == "person-123"
		assert face.tag_text == "Jane Smith (Manager)"
		assert face.confidence == 0.92
		assert face.is_known == True
		assert face.source_group == "known_primary"
		assert face.bounding_box == (50, 60, 150, 180)
		assert face.cropped_image is not None
		assert face.cropped_image.shape == (200, 200, 3)

	def test_detected_face_creation_unknown(self):
		"""Test creating DetectedFace for unknown person"""
		face = DetectedFace(
			face_id="face-unknown-1",
			person_id=None,
			tag_text="person_1",
			confidence=0.75,
			is_known=False,
			source_group="unknown_primary",
			bounding_box=(30, 40, 130, 160),
			cropped_image=None
		)

		assert face.person_id is None
		assert face.is_known == False
		assert face.tag_text == "person_1"
		assert face.source_group == "unknown_primary"
		assert face.cropped_image is None


class TestFaceGroupManager:
	"""Tests for FaceGroupManager - coordination and frame processing"""

	@patch('face_recognition.face_group_manager.FaceClient')
	@patch('face_recognition.face_group_manager.KnownFaceGroup')
	@patch('face_recognition.face_group_manager.UnknownFaceGroup')
	def test_initialization(self, mock_unknown_group, mock_known_group, mock_face_client, azure_credentials):
		"""Test FaceGroupManager initialization"""
		manager = FaceGroupManager(
			endpoint=azure_credentials["endpoint"],
			api_key=azure_credentials["api_key"],
			group_id="test_group",
			group_name="Test Group"
		)

		assert manager.endpoint == azure_credentials["endpoint"]
		assert manager.api_key == azure_credentials["api_key"]
		assert manager.group_id == "test_group"
		assert manager.group_name == "Test Group"
		mock_face_client.assert_called_once()
		mock_known_group.assert_called_once()
		mock_unknown_group.assert_called_once()

	@patch('face_recognition.face_group_manager.FaceClient')
	@patch('face_recognition.face_group_manager.KnownFaceGroup')
	@patch('face_recognition.face_group_manager.UnknownFaceGroup')
	def test_process_frame_no_faces(self, mock_unknown_group, mock_known_group, mock_face_client, azure_credentials, sample_frame):
		"""Test processing frame with no detected faces"""
		# Mock face detection to return no faces
		mock_client_instance = Mock()
		mock_face_client.return_value = mock_client_instance
		mock_client_instance.detect.return_value = []

		manager = FaceGroupManager(
			endpoint=azure_credentials["endpoint"],
			api_key=azure_credentials["api_key"],
			group_id="test_group",
			group_name="Test Group"
		)

		with patch.object(manager, '_detect_faces_in_frame', return_value=[]):
			result = manager.process_frame(sample_frame)

			assert isinstance(result, FrameProcessingResult)
			assert result.total_faces == 0
			assert result.known_faces == 0
			assert result.unknown_faces == 0
			assert len(result.detected_faces) == 0

	@patch('face_recognition.face_group_manager.FaceClient')
	@patch('face_recognition.face_group_manager.KnownFaceGroup')
	@patch('face_recognition.face_group_manager.UnknownFaceGroup')
	def test_image_to_bytes(self, mock_unknown_group, mock_known_group, mock_face_client, azure_credentials, sample_image):
		"""Test converting image to bytes"""
		manager = FaceGroupManager(
			endpoint=azure_credentials["endpoint"],
			api_key=azure_credentials["api_key"],
			group_id="test_group",
			group_name="Test Group"
		)

		image_bytes = manager._image_to_bytes(sample_image)
		assert isinstance(image_bytes, bytes)
		assert len(image_bytes) > 0


class TestBaseGroupManager:
	"""Tests for BaseGroupManager - Azure API operations"""

	@patch('face_recognition.base_group_manager.FaceAdministrationClient')
	def test_initialization(self, mock_admin_client, azure_credentials):
		"""Test BaseGroupManager initialization"""
		manager = BaseGroupManager(
			endpoint=azure_credentials["endpoint"],
			api_key=azure_credentials["api_key"],
			max_faces_per_person=5,
			face_similarity_threshold=0.9
		)

		assert manager.endpoint == azure_credentials["endpoint"]
		assert manager.api_key == azure_credentials["api_key"]
		assert manager.max_faces_per_person == 5
		assert manager.face_similarity_threshold == 0.9
		mock_admin_client.assert_called_once()

	@patch('face_recognition.base_group_manager.FaceAdministrationClient')
	def test_create_group_if_not_exists_new_group(self, mock_admin_client, azure_credentials):
		"""Test creating new group when it doesn't exist"""
		mock_client_instance = Mock()
		mock_admin_client.return_value = mock_client_instance

		# Mock group doesn't exist (get raises exception)
		mock_client_instance.large_person_group.get.side_effect = Exception("Group not found")
		mock_client_instance.large_person_group.create.return_value = None

		manager = BaseGroupManager(
			endpoint=azure_credentials["endpoint"],
			api_key=azure_credentials["api_key"]
		)

		result = manager.create_group_if_not_exists("new_group", "New Group")

		assert result == True
		mock_client_instance.large_person_group.create.assert_called_once()

	@patch('face_recognition.base_group_manager.FaceAdministrationClient')
	def test_create_group_if_not_exists_existing_group(self, mock_admin_client, azure_credentials):
		"""Test handling existing group"""
		mock_client_instance = Mock()
		mock_admin_client.return_value = mock_client_instance

		# Mock group exists
		mock_client_instance.large_person_group.get.return_value = Mock(large_person_group_id="existing_group")

		manager = BaseGroupManager(
			endpoint=azure_credentials["endpoint"],
			api_key=azure_credentials["api_key"]
		)

		result = manager.create_group_if_not_exists("existing_group", "Existing Group")

		assert result == True
		mock_client_instance.large_person_group.create.assert_not_called()


# Cleanup functionality
class TestCleanup:
	"""Tests for cleanup functionality to remove test groups"""

	def test_cleanup_all_groups_success(self, azure_credentials):
		"""Test successful cleanup of all test groups"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager
			mock_group_manager.cleanup_all_groups.return_value = True

			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_cleanup_group",
				group_name="Test Cleanup Group"
			)

			result = manager.cleanup_all_groups()
			assert result == True
			mock_group_manager.cleanup_all_groups.assert_called_once()


class TestImageSaving:
	"""Test image saving functionality"""

	def test_save_unknown_face_image(self, azure_credentials, sample_image):
		"""Test saving unknown face image to disk"""
		with patch('azure.ai.vision.face.FaceClient'), \
			 patch('azure.ai.vision.face.FaceAdministrationClient'), \
			 patch('cv2.imwrite', return_value=True) as mock_imwrite, \
			 patch('pathlib.Path.mkdir') as mock_mkdir, \
			 patch('pathlib.Path.exists', return_value=True):

			manager = BaseGroupManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				max_faces_per_person=5,
				face_similarity_threshold=0.8
			)

			# Test saving image
			result = manager.save_unknown_face_image(sample_image, "person_1", "face_123")

			# Verify calls
			mock_mkdir.assert_called()
			mock_imwrite.assert_called_once()
			assert result is not None
			assert "person_1" in result
			assert "face_123" in result

	def test_save_known_face_image(self, azure_credentials, sample_image):
		"""Test saving known face image with metadata"""
		with patch('azure.ai.vision.face.FaceClient'), \
			 patch('azure.ai.vision.face.FaceAdministrationClient'), \
			 patch('cv2.imwrite', return_value=True) as mock_imwrite, \
			 patch('pathlib.Path.mkdir') as mock_mkdir, \
			 patch('pathlib.Path.exists', return_value=True), \
			 patch('builtins.open', create=True) as mock_open:

			manager = BaseGroupManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				max_faces_per_person=5,
				face_similarity_threshold=0.8
			)

			# Mock file operations
			mock_file = Mock()
			mock_open.return_value.__enter__.return_value = mock_file

			# Test saving image
			result = manager.save_known_face_image(
				sample_image, "person_1", "face_123", "John Doe", "Engineer"
			)

			# Verify calls
			mock_mkdir.assert_called()
			mock_imwrite.assert_called_once()
			mock_open.assert_called()
			assert result is not None
			assert "person_1" in result
			assert "face_123" in result

	def test_get_unknown_person_images(self, azure_credentials):
		"""Test getting list of unknown person images"""
		with patch('azure.ai.vision.face.FaceClient'), \
			 patch('azure.ai.vision.face.FaceAdministrationClient'):

			manager = BaseGroupManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				max_faces_per_person=5,
				face_similarity_threshold=0.8
			)

			# Mock the person directory and its methods
			mock_person_dir = Mock()
			mock_person_dir.exists.return_value = True
			mock_files = [Path("face_1.jpg"), Path("face_2.jpg")]
			mock_person_dir.glob.return_value = mock_files

			# Mock the unknown_images_dir to return our mock person dir
			with patch.object(manager, 'unknown_images_dir') as mock_unknown_dir:
				mock_unknown_dir.__truediv__.return_value = mock_person_dir

				# Test getting images
				result = manager.get_unknown_person_images("person_1")

				# Verify result
				assert len(result) == 2
				assert all("face_" in path for path in result)


class TestUnknownToKnownTransfer:
	"""Test unknown to known person transfer functionality"""

	@patch('face_recognition.face_group_manager.FaceGroupManager._remove_unknown_person_from_groups')
	@patch('face_recognition.face_group_manager.FaceGroupManager._add_saved_images_to_known_person')
	def test_transfer_unknown_to_known_person(self, mock_add_images, mock_remove_person, azure_credentials):
		"""Test transferring unknown person to known group"""
		with patch('face_recognition.face_group_manager.KnownFaceGroup') as mock_known_class, \
			 patch('face_recognition.face_group_manager.UnknownFaceGroup') as mock_unknown_class, \
			 patch('face_recognition.face_group_manager.FaceClient'):

			# Setup mocks
			mock_known = Mock()
			mock_unknown = Mock()
			mock_known_class.return_value = mock_known
			mock_unknown_class.return_value = mock_unknown

			# Mock unknown person lookup
			mock_unknown.get_unknown_persons_list.return_value = [
				{"name": "person_1", "person_id": "unknown_123"}
			]

			# Mock known person creation
			mock_known.create_known_person.return_value = "known_456"

			# Mock image operations
			mock_unknown.get_unknown_person_images.return_value = ["image1.jpg", "image2.jpg"]
			mock_unknown.transfer_unknown_to_known.return_value = True

			# Create manager
			manager = FaceGroupManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_transfer",
				group_name="Transfer Test"
			)

			# Test transfer
			result = manager.transfer_unknown_to_known_person(
				unknown_person_label="person_1",
				real_name="John Doe",
				user_data="Engineer"
			)

			# Verify result
			assert result == "known_456"
			mock_known.create_known_person.assert_called_once_with("John Doe", "Engineer")
			mock_unknown.transfer_unknown_to_known.assert_called_once()
			mock_add_images.assert_called_once()
			mock_remove_person.assert_called_once_with("unknown_123")

	def test_transfer_unknown_person_not_found(self, azure_credentials):
		"""Test transfer when unknown person is not found"""
		with patch('face_recognition.face_group_manager.KnownFaceGroup') as mock_known_class, \
			 patch('face_recognition.face_group_manager.UnknownFaceGroup') as mock_unknown_class, \
			 patch('face_recognition.face_group_manager.FaceClient'):

			# Setup mocks
			mock_unknown = Mock()
			mock_unknown_class.return_value = mock_unknown

			# Mock empty unknown persons list
			mock_unknown.get_unknown_persons_list.return_value = []

			# Create manager
			manager = FaceGroupManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_transfer",
				group_name="Transfer Test"
			)

			# Test transfer with non-existent person
			result = manager.transfer_unknown_to_known_person(
				unknown_person_label="nonexistent_person",
				real_name="John Doe",
				user_data="Engineer"
			)

			# Verify result
			assert result is None


class TestSaveKnownImagesParameter:
	"""Test save_known_images parameter functionality"""

	def test_face_recognition_manager_with_save_known_images_enabled(self, azure_credentials):
		"""Test FaceRecognitionManager with save_known_images=True"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager

			# Create manager with save_known_images enabled
			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_save",
				group_name="Save Test",
				save_known_images=True
			)

			# Verify save_known_images parameter was passed
			assert manager.save_known_images == True
			mock_group_manager_class.assert_called_once()
			call_args = mock_group_manager_class.call_args
			assert call_args.kwargs['save_known_images'] == True

	def test_face_recognition_manager_with_save_known_images_disabled(self, azure_credentials):
		"""Test FaceRecognitionManager with save_known_images=False (default)"""
		with patch('face_recognition.face_recognition_manager.FaceGroupManager') as mock_group_manager_class:
			mock_group_manager = Mock()
			mock_group_manager_class.return_value = mock_group_manager

			# Create manager with default save_known_images
			manager = FaceRecognitionManager(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_save",
				group_name="Save Test"
			)

			# Verify save_known_images parameter default
			assert manager.save_known_images == False
			mock_group_manager_class.assert_called_once()
			call_args = mock_group_manager_class.call_args
			assert call_args.kwargs['save_known_images'] == False

	def test_known_face_group_saves_images_when_enabled(self, azure_credentials):
		"""Test KnownFaceGroup parameter save_known_images=True is properly set"""
		with patch('azure.ai.vision.face.FaceAdministrationClient'), \
			 patch('azure.ai.vision.face.FaceClient'):

			# Create known face group with save_known_images enabled
			known_group = KnownFaceGroup(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_save",
				group_name="Save Test",
				save_known_images=True
			)

			# Verify the parameter was set correctly
			assert known_group.save_known_images == True

	def test_known_face_group_skips_images_when_disabled(self, azure_credentials):
		"""Test KnownFaceGroup parameter save_known_images=False is properly set"""
		with patch('azure.ai.vision.face.FaceAdministrationClient'), \
			 patch('azure.ai.vision.face.FaceClient'):

			# Create known face group with save_known_images disabled
			known_group = KnownFaceGroup(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_save",
				group_name="Save Test",
				save_known_images=False
			)

			# Verify the parameter was set correctly
			assert known_group.save_known_images == False


class TestUnknownImageAutoSaving:
	"""Test automatic saving of unknown face images"""

	def test_unknown_face_auto_save_on_creation(self, azure_credentials):
		"""Test UnknownFaceGroup initialization works correctly"""
		with patch('azure.ai.vision.face.FaceAdministrationClient'), \
			 patch('azure.ai.vision.face.FaceClient'):

			# Create unknown face group
			unknown_group = UnknownFaceGroup(
				endpoint=azure_credentials["endpoint"],
				api_key=azure_credentials["api_key"],
				group_id="test_auto_save",
				group_name="Auto Save Test",
				max_faces_per_person=5,
				face_similarity_threshold=0.8,
				unknown_confidence_threshold=0.8,
				auto_train_unknown=True
			)

			# Verify the group was created with correct parameters
			assert unknown_group.max_faces_per_person == 5
			assert unknown_group.face_similarity_threshold == 0.8
			assert unknown_group.unknown_confidence_threshold == 0.8
			assert unknown_group.auto_train_unknown == True


# Test runner cleanup function
def cleanup_test_groups():
	"""
	Cleanup function to remove all test groups after testing.

	This function should be called at the end of testing to ensure
	no test groups remain in the Azure Face API service.
	"""
	try:
		print("Cleaning up test groups...")

		# Check if real Azure credentials are available
		endpoint = os.getenv('AZURE_FACE_ENDPOINT')
		api_key = os.getenv('AZURE_FACE_API_KEY')

		if endpoint and api_key:
			print("Found Azure credentials, performing real cleanup...")

			# Add project root to path for imports
			project_root = Path(__file__).parent.parent
			if str(project_root) not in sys.path:
				sys.path.insert(0, str(project_root))

			# Import Azure clients
			from azure.ai.vision.face import FaceAdministrationClient
			from azure.core.credentials import AzureKeyCredential

			# Initialize client
			credential = AzureKeyCredential(api_key)
			admin_client = FaceAdministrationClient(endpoint, credential)

			# Get all large person groups
			groups = admin_client.large_person_group.get_large_person_groups()

			# Delete test groups (groups that start with "test_")
			deleted_count = 0
			for group in groups:
				if group.large_person_group_id.startswith("test_"):
					try:
						admin_client.large_person_group.delete(group.large_person_group_id)
						print(f"Deleted test group: {group.large_person_group_id}")
						deleted_count += 1
					except Exception as e:
						print(f"Failed to delete group {group.large_person_group_id}: {e}")

			if deleted_count > 0:
				print(f"Cleanup completed - deleted {deleted_count} test groups")
			else:
				print("Cleanup completed - no test groups found")
		else:
			print("No Azure credentials found - skipping real cleanup")
			print("Set AZURE_FACE_ENDPOINT and AZURE_FACE_API_KEY environment variables for real cleanup")
			print("Test groups cleanup completed (mock)")

		return True
	except Exception as e:
		print(f"Error during cleanup: {e}")
		return False


# Pytest hooks for cleanup
def pytest_sessionfinish(session, exitstatus):
	"""Called after whole test run finished, right before returning the exit status"""
	# Cleanup is handled by the test runner, so we don't need to call it here
	# to avoid duplicate cleanup calls
	pass
