<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">32%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 03:44 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6___init___py.html">face_recognition\__init__.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t34">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t34"><data value='init__'>BaseGroupManager.__init__</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t70">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t70"><data value='setup_logger'>BaseGroupManager._setup_logger</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t84">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t84"><data value='create_group_if_not_exists'>BaseGroupManager.create_group_if_not_exists</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t106">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t106"><data value='create_person_in_group'>BaseGroupManager.create_person_in_group</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t124">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t124"><data value='add_face_to_person_in_group'>BaseGroupManager.add_face_to_person_in_group</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t147">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t147"><data value='delete_person_from_group'>BaseGroupManager.delete_person_from_group</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t157">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t157"><data value='get_persons_in_group'>BaseGroupManager.get_persons_in_group</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t165">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t165"><data value='set_identification_in_progress'>BaseGroupManager.set_identification_in_progress</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t170">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t170"><data value='is_identification_in_progress'>BaseGroupManager.is_identification_in_progress</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t175">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t175"><data value='add_to_training_queue'>BaseGroupManager.add_to_training_queue</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t193">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t193"><data value='process_training_queue'>BaseGroupManager._process_training_queue</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t215">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t215"><data value='train_group_with_callback'>BaseGroupManager._train_group_with_callback</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t228">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t228"><data value='monitor_training'>BaseGroupManager._train_group_with_callback.monitor_training</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t255">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t255"><data value='train_group_async'>BaseGroupManager.train_group_async</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t259">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t259"><data value='get_training_status'>BaseGroupManager.get_training_status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t280">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t280"><data value='image_to_bytes'>BaseGroupManager._image_to_bytes</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t302">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t302"><data value='crop_face_from_frame'>BaseGroupManager._crop_face_from_frame</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t330">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t330"><data value='assess_face_quality'>BaseGroupManager._assess_face_quality</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t355">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t355"><data value='cleanup_group'>BaseGroupManager.cleanup_group</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t60">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t60"><data value='init__'>FaceGroupManager.__init__</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t103">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t103"><data value='setup_logger'>FaceGroupManager._setup_logger</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t118">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t118"><data value='process_frame'>FaceGroupManager.process_frame</data></a></td>
                <td>20</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="8 20">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t188">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t188"><data value='detect_faces_in_frame'>FaceGroupManager._detect_faces_in_frame</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t214">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t214"><data value='dual_identify'>FaceGroupManager._dual_identify</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t237">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t237"><data value='image_to_bytes'>FaceGroupManager._image_to_bytes</data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t260">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t260"><data value='process_identification_results'>FaceGroupManager._process_identification_results</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t362">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t362"><data value='crop_face_from_frame'>FaceGroupManager._crop_face_from_frame</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t390">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t390"><data value='annotate_frame'>FaceGroupManager._annotate_frame</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t439">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t439"><data value='get_group_statistics'>FaceGroupManager.get_group_statistics</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t463">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t463"><data value='get_training_status'>FaceGroupManager.get_training_status</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t479">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t479"><data value='create_known_person'>FaceGroupManager.create_known_person</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t483">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t483"><data value='add_face_to_known_person'>FaceGroupManager.add_face_to_known_person</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t487">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t487"><data value='get_known_persons'>FaceGroupManager.get_known_persons</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t492">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t492"><data value='update_unknown_person_with_real_name'>FaceGroupManager.update_unknown_person_with_real_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t496">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t496"><data value='get_unknown_persons_list'>FaceGroupManager.get_unknown_persons_list</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t501">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t501"><data value='get_active_known_group_id'>FaceGroupManager.get_active_known_group_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t505">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t505"><data value='get_active_unknown_group_id'>FaceGroupManager.get_active_unknown_group_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t509">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t509"><data value='cleanup_all_groups'>FaceGroupManager.cleanup_all_groups</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t29">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t29"><data value='init__'>FaceRecognitionManager.__init__</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t87">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t87"><data value='setup_logger'>FaceRecognitionManager._setup_logger</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t102">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t102"><data value='process_frame'>FaceRecognitionManager.process_frame</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t126">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t126"><data value='create_known_person'>FaceRecognitionManager.create_known_person</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t139">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t139"><data value='add_face_to_known_person'>FaceRecognitionManager.add_face_to_known_person</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t153">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t153"><data value='get_known_persons'>FaceRecognitionManager.get_known_persons</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t163">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t163"><data value='update_unknown_person_with_real_name'>FaceRecognitionManager.update_unknown_person_with_real_name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t177">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t177"><data value='get_unknown_persons_list'>FaceRecognitionManager.get_unknown_persons_list</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t187">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t187"><data value='get_group_statistics'>FaceRecognitionManager.get_group_statistics</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t196">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t196"><data value='get_training_status'>FaceRecognitionManager.get_training_status</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t206">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t206"><data value='get_active_known_group_id'>FaceRecognitionManager.get_active_known_group_id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t210">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t210"><data value='get_active_unknown_group_id'>FaceRecognitionManager.get_active_unknown_group_id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t215">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t215"><data value='cleanup_all_groups'>FaceRecognitionManager.cleanup_all_groups</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t224">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t224"><data value='save_cropped_faces'>FaceRecognitionManager.save_cropped_faces</data></a></td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="16 18">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t261">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t261"><data value='str__'>FaceRecognitionManager.__str__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t265">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t265"><data value='repr__'>FaceRecognitionManager.__repr__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t26">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t26"><data value='init__'>KnownFaceGroup.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t56">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t56"><data value='initialize_groups'>KnownFaceGroup._initialize_groups</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t66">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t66"><data value='get_active_group_id'>KnownFaceGroup.get_active_group_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t70">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t70"><data value='get_standby_group_id'>KnownFaceGroup.get_standby_group_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t74">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t74"><data value='switch_active_group'>KnownFaceGroup.switch_active_group</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t82">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t82"><data value='identify_faces'>KnownFaceGroup.identify_faces</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t99">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t99"><data value='add_face_to_known_person_if_needed'>KnownFaceGroup.add_face_to_known_person_if_needed</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t141">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t141"><data value='trigger_training_if_needed'>KnownFaceGroup._trigger_training_if_needed</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t153">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t153"><data value='after_primary_training'>KnownFaceGroup._trigger_training_if_needed.after_primary_training</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t180">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t180"><data value='queue_primary_training'>KnownFaceGroup._queue_primary_training</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t190">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t190"><data value='after_training'>KnownFaceGroup._queue_primary_training.after_training</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t197">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t197"><data value='queue_secondary_training'>KnownFaceGroup._queue_secondary_training</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t207">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t207"><data value='after_training'>KnownFaceGroup._queue_secondary_training.after_training</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t214">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t214"><data value='train_group_async'>KnownFaceGroup.train_group_async</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t217">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t217"><data value='after_training'>KnownFaceGroup.train_group_async.after_training</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t225">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t225"><data value='get_training_status'>KnownFaceGroup.get_training_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t232">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t232"><data value='get_group_statistics'>KnownFaceGroup.get_group_statistics</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t265">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t265"><data value='create_known_person'>KnownFaceGroup.create_known_person</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t270">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t270"><data value='add_face_to_known_person'>KnownFaceGroup.add_face_to_known_person</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t275">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t275"><data value='get_known_persons'>KnownFaceGroup.get_known_persons</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t291">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t291"><data value='cleanup_known_groups'>KnownFaceGroup.cleanup_known_groups</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t27">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t27"><data value='init__'>UnknownFaceGroup.__init__</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t64">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t64"><data value='initialize_groups'>UnknownFaceGroup._initialize_groups</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t77">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t77"><data value='initialize_unknown_counter'>UnknownFaceGroup._initialize_unknown_counter</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t98">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t98"><data value='get_active_group_id'>UnknownFaceGroup.get_active_group_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t102">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t102"><data value='get_standby_group_id'>UnknownFaceGroup.get_standby_group_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t106">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t106"><data value='switch_active_group'>UnknownFaceGroup.switch_active_group</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t114">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t114"><data value='identify_faces'>UnknownFaceGroup.identify_faces</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t131">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t131"><data value='create_unknown_person'>UnknownFaceGroup.create_unknown_person</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t171">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t171"><data value='queue_unknown_face_for_training'>UnknownFaceGroup._queue_unknown_face_for_training</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t180">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t180"><data value='trigger_training_if_needed'>UnknownFaceGroup._trigger_training_if_needed</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t192">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t192"><data value='after_primary_training'>UnknownFaceGroup._trigger_training_if_needed.after_primary_training</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t219">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t219"><data value='queue_primary_training'>UnknownFaceGroup._queue_primary_training</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t229">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t229"><data value='after_training'>UnknownFaceGroup._queue_primary_training.after_training</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t236">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t236"><data value='queue_secondary_training'>UnknownFaceGroup._queue_secondary_training</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t246">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t246"><data value='after_training'>UnknownFaceGroup._queue_secondary_training.after_training</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t253">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t253"><data value='train_group_async'>UnknownFaceGroup.train_group_async</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t256">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t256"><data value='after_training'>UnknownFaceGroup.train_group_async.after_training</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t264">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t264"><data value='update_unknown_person_with_real_name'>UnknownFaceGroup.update_unknown_person_with_real_name</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t308">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t308"><data value='get_unknown_persons_list'>UnknownFaceGroup.get_unknown_persons_list</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t342">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t342"><data value='get_training_status'>UnknownFaceGroup.get_training_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t349">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t349"><data value='get_group_statistics'>UnknownFaceGroup.get_group_statistics</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t382">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t382"><data value='cleanup_unknown_groups'>UnknownFaceGroup.cleanup_unknown_groups</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>868</td>
                <td>591</td>
                <td>0</td>
                <td class="right" data-ratio="277 868">32%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 03:44 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
