["tests/test_face_recognition.py::TestBaseGroupManager::test_create_group_if_not_exists_existing_group", "tests/test_face_recognition.py::TestBaseGroupManager::test_create_group_if_not_exists_new_group", "tests/test_face_recognition.py::TestBaseGroupManager::test_initialization", "tests/test_face_recognition.py::TestCleanup::test_cleanup_all_groups_success", "tests/test_face_recognition.py::TestDetectedFace::test_detected_face_creation", "tests/test_face_recognition.py::TestDetectedFace::test_detected_face_creation_known", "tests/test_face_recognition.py::TestDetectedFace::test_detected_face_creation_unknown", "tests/test_face_recognition.py::TestDetectedFace::test_detected_face_unknown", "tests/test_face_recognition.py::TestErrorHandling::test_empty_person_id", "tests/test_face_recognition.py::TestErrorHandling::test_invalid_credentials", "tests/test_face_recognition.py::TestErrorHandling::test_none_image_handling", "tests/test_face_recognition.py::TestFaceGroupManager::test_image_to_bytes", "tests/test_face_recognition.py::TestFaceGroupManager::test_initialization", "tests/test_face_recognition.py::TestFaceGroupManager::test_process_frame_no_faces", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_add_face_to_known_person", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_cleanup_all_groups", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_create_known_person", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_get_active_known_group_id", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_get_active_unknown_group_id", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_get_group_statistics", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_get_known_persons", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_get_training_status", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_get_unknown_persons_list", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_initialization", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_initialization_custom_parameters", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_initialization_default_parameters", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_process_frame", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_save_cropped_faces", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_setup_logger", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_string_representation", "tests/test_face_recognition.py::TestFaceRecognitionManager::test_update_unknown_person_with_real_name", "tests/test_face_recognition.py::TestFrameProcessingResult::test_frame_processing_result_creation", "tests/test_face_recognition.py::TestFrameProcessingResult::test_frame_processing_result_empty", "tests/test_face_recognition.py::TestImageSaving::test_get_unknown_person_images", "tests/test_face_recognition.py::TestImageSaving::test_save_known_face_image", "tests/test_face_recognition.py::TestImageSaving::test_save_unknown_face_image", "tests/test_face_recognition.py::TestIntegration::test_full_workflow_simulation", "tests/test_face_recognition.py::TestSaveKnownImagesParameter::test_face_recognition_manager_with_save_known_images_disabled", "tests/test_face_recognition.py::TestSaveKnownImagesParameter::test_face_recognition_manager_with_save_known_images_enabled", "tests/test_face_recognition.py::TestSaveKnownImagesParameter::test_known_face_group_saves_images_when_enabled", "tests/test_face_recognition.py::TestSaveKnownImagesParameter::test_known_face_group_skips_images_when_disabled", "tests/test_face_recognition.py::TestUnknownImageAutoSaving::test_unknown_face_auto_save_on_creation", "tests/test_face_recognition.py::TestUnknownToKnownTransfer::test_transfer_unknown_person_not_found", "tests/test_face_recognition.py::TestUnknownToKnownTransfer::test_transfer_unknown_to_known_person"]