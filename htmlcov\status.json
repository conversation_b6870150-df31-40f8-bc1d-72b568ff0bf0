{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "3768167b240d0a9bb51fdcee17127d9b", "files": {"z_3b431d04926d1de6___init___py": {"hash": "3ea6682ee53bebf344c2d9297b98acd2", "index": {"url": "z_3b431d04926d1de6___init___py.html", "file": "face_recognition\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3b431d04926d1de6_base_group_manager_py": {"hash": "4f33aa0cca043db287b8f23c527e925a", "index": {"url": "z_3b431d04926d1de6_base_group_manager_py.html", "file": "face_recognition\\base_group_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 200, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3b431d04926d1de6_face_group_manager_py": {"hash": "10453b0fffd24780144cad578d94f2f2", "index": {"url": "z_3b431d04926d1de6_face_group_manager_py.html", "file": "face_recognition\\face_group_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 240, "n_excluded": 0, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3b431d04926d1de6_face_recognition_manager_py": {"hash": "891c3f3a88b3c28075a73fe2fa90d452", "index": {"url": "z_3b431d04926d1de6_face_recognition_manager_py.html", "file": "face_recognition\\face_recognition_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3b431d04926d1de6_known_face_group_py": {"hash": "79bf113dc5956fa9adc3664473c11e35", "index": {"url": "z_3b431d04926d1de6_known_face_group_py.html", "file": "face_recognition\\known_face_group.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 149, "n_excluded": 0, "n_missing": 128, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3b431d04926d1de6_unknown_face_group_py": {"hash": "b4b314b68a45c08582444cdb5c48ebde", "index": {"url": "z_3b431d04926d1de6_unknown_face_group_py.html", "file": "face_recognition\\unknown_face_group.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 194, "n_excluded": 0, "n_missing": 171, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}