# coding: utf-8

"""
FILE: unknown_face_group.py

DESCRIPTION:
    Unknown face group management class that handles operations for unknown persons.
    Manages primary/secondary unknown groups with auto-training capabilities.

FEATURES:
    - Dual unknown group management (primary/secondary)
    - Unknown person creation and management
    - Update unknown persons with real names
    - Auto-training for unknown faces
    - Active group switching
"""

import numpy as np
from typing import List, Dict, Any, Optional
from datetime import datetime
from .base_group_manager import BaseGroupManager


class UnknownFaceGroup(BaseGroupManager):
	"""Manages unknown face groups with dual primary/secondary architecture"""
	
	def __init__(
		self,
		endpoint: str,
		api_key: str,
		group_id: str,
		group_name: str,
		max_faces_per_person: int = 10,
		face_similarity_threshold: float = 0.8,
		unknown_confidence_threshold: float = 0.8,
		auto_train_unknown: bool = True,
		logger: Optional[Any] = None
	):
		"""Initialize unknown face group manager"""
		super().__init__(endpoint, api_key, max_faces_per_person, face_similarity_threshold, logger)
		
		self.base_group_id = group_id
		self.base_group_name = group_name
		self.unknown_confidence_threshold = unknown_confidence_threshold
		self.auto_train_unknown = auto_train_unknown
		
		# Unknown group IDs
		self.primary_group_id = f"{group_id}_unknown_primary"
		self.secondary_group_id = f"{group_id}_unknown_secondary"
		
		# Training states for unknown groups
		self.primary_training = False
		self.secondary_training = False
		
		# Active group tracking
		self.active_group = "primary"  # primary or secondary
		
		# Unknown person counter
		self.unknown_counter = 0
		
		# Initialize groups
		self._initialize_groups()
	
	def _initialize_groups(self):
		"""Initialize both unknown groups"""
		primary_name = f"{self.base_group_name} - Unknown Primary"
		secondary_name = f"{self.base_group_name} - Unknown Secondary"
		
		self.create_group_if_not_exists(self.primary_group_id, primary_name)
		self.create_group_if_not_exists(self.secondary_group_id, secondary_name)
		
		# Initialize unknown counter based on existing persons
		self._initialize_unknown_counter()
		
		self.logger.info(f"Initialized unknown groups: {self.primary_group_id}, {self.secondary_group_id}")
	
	def _initialize_unknown_counter(self):
		"""Initialize unknown counter based on existing persons"""
		try:
			all_persons = self.get_unknown_persons_list()
			max_counter = 0
			
			for person in all_persons:
				if person['name'].startswith('person_'):
					try:
						counter = int(person['name'].split('_')[1])
						max_counter = max(max_counter, counter)
					except (IndexError, ValueError):
						continue
			
			self.unknown_counter = max_counter
			self.logger.info(f"Initialized unknown counter to: {self.unknown_counter}")
			
		except Exception as e:
			self.logger.warning(f"Error initializing unknown counter: {e}")
			self.unknown_counter = 0
	
	def get_active_group_id(self) -> str:
		"""Get the currently active unknown group ID"""
		return self.primary_group_id if self.active_group == "primary" else self.secondary_group_id
	
	def get_standby_group_id(self) -> str:
		"""Get the standby unknown group ID"""
		return self.secondary_group_id if self.active_group == "primary" else self.primary_group_id
	
	def switch_active_group(self):
		"""Switch active group (used when training starts)"""
		old_active = self.active_group
		self.active_group = "secondary" if self.active_group == "primary" else "primary"
		new_active = self.active_group
		
		self.logger.info(f"Switched unknown active group: {old_active} → {new_active}")
	
	def identify_faces(self, face_ids: List[str], confidence_threshold: float = 0.6) -> List[Any]:
		"""Identify faces against active unknown group"""
		try:
			active_group_id = self.get_active_group_id()
			
			identification_results = self.face_client.identify(
				face_ids=face_ids,
				large_person_group_id=active_group_id
			)
			
			self.logger.debug(f"Unknown identification completed against {active_group_id}")
			return identification_results
			
		except Exception as e:
			self.logger.error(f"Error in unknown face identification: {e}")
			return []
	
	def create_unknown_person(self, face_image: np.ndarray) -> tuple[str, str] | None:
		"""Create a new unknown person and add the face"""
		try:
			# Generate unknown person name
			self.unknown_counter += 1
			unknown_label = f"person_{self.unknown_counter}"
			
			active_group_id = self.get_active_group_id()
			
			# Create person
			person_id = self.create_person_in_group(
				active_group_id,
				unknown_label,
				f"Auto-created unknown person from video frame"
			)
			
			if person_id:
				# Add face to person
				face_id = self.add_face_to_person_in_group(
					active_group_id,
					person_id,
					face_image,
					f"Initial face for unknown person {unknown_label}"
				)
				
				if face_id:
					# Save unknown face image to disk (always save for unknown faces)
					self.save_unknown_face_image(face_image, person_id, face_id)

					self.logger.info(f"Created unknown person: {unknown_label} (ID: {person_id})")

					# Queue for training if auto-training is enabled
					if self.auto_train_unknown:
						self._queue_unknown_face_for_training(active_group_id)

					return person_id, unknown_label
			
			return None
			
		except Exception as e:
			self.logger.error(f"Error creating unknown person: {e}")
			return None
	
	def _queue_unknown_face_for_training(self, group_id: str):
		"""Queue unknown face for training"""
		try:
			# Trigger training for the group
			self._trigger_training_if_needed(group_id)
			
		except Exception as e:
			self.logger.error(f"Error queuing unknown face for training: {e}")
	
	def _trigger_training_if_needed(self, group_id: str):
		"""Trigger smart training for unknown group if not already training"""
		try:
			# Determine which group and check if training
			if group_id == self.primary_group_id:
				if not self.primary_training:
					self.primary_training = True
					# Switch to secondary group for identification
					if self.active_group == "primary":
						self.switch_active_group()

					# Add to smart training queue with callback to train secondary
					def after_primary_training():
						self.primary_training = False
						# Queue secondary group for training if it has faces
						if not self.secondary_training:
							self._queue_secondary_training()

					self.add_to_training_queue(group_id, "unknown_primary", after_primary_training)

			elif group_id == self.secondary_group_id:
				if not self.secondary_training:
					self.secondary_training = True
					# Switch to primary group for identification
					if self.active_group == "secondary":
						self.switch_active_group()

					# Add to smart training queue with callback to train primary
					def after_secondary_training():
						self.secondary_training = False
						# Queue primary group for training if it has faces
						if not self.primary_training:
							self._queue_primary_training()

					self.add_to_training_queue(group_id, "unknown_secondary", after_secondary_training)

		except Exception as e:
			self.logger.error(f"Error triggering smart training for unknown group {group_id}: {e}")

	def _queue_primary_training(self):
		"""Queue primary group for training if it needs training"""
		try:
			# Check if primary group has enough faces to warrant training
			persons = self.face_admin_client.large_person_group.list_persons(self.primary_group_id)
			total_faces = sum(len(person.persisted_face_ids or []) for person in persons)

			if total_faces > 0:  # Has faces to train
				self.logger.info(f"Queueing primary unknown group for training ({total_faces} faces)")

				def after_training():
					self.primary_training = False

				self.add_to_training_queue(self.primary_group_id, "unknown_primary", after_training)
		except Exception as e:
			self.logger.error(f"Error queueing primary training: {e}")

	def _queue_secondary_training(self):
		"""Queue secondary group for training if it needs training"""
		try:
			# Check if secondary group has enough faces to warrant training
			persons = self.face_admin_client.large_person_group.list_persons(self.secondary_group_id)
			total_faces = sum(len(person.persisted_face_ids or []) for person in persons)

			if total_faces > 0:  # Has faces to train
				self.logger.info(f"Queueing secondary unknown group for training ({total_faces} faces)")

				def after_training():
					self.secondary_training = False

				self.add_to_training_queue(self.secondary_group_id, "unknown_secondary", after_training)
		except Exception as e:
			self.logger.error(f"Error queueing secondary training: {e}")
	
	def train_group_async(self, group_id: str, group_type: str):
		"""Start asynchronous training for unknown group (legacy method)"""
		# Use the new smart training system
		def after_training():
			if group_type == "unknown_primary":
				self.primary_training = False
			elif group_type == "unknown_secondary":
				self.secondary_training = False

		self.add_to_training_queue(group_id, group_type, after_training)

	def get_unknown_person_images(self, person_id: str) -> List[str]:
		"""Get list of saved image paths for an unknown person"""
		return self.get_unknown_person_images(person_id)
	
	def update_unknown_person_with_real_name(self, person_label: str, real_name: str, user_data: str = "") -> bool:
		"""Update an unknown person (person_X) with real name and user_data"""
		try:
			# Search for the person in both unknown groups
			unknown_groups = [
				(self.primary_group_id, "primary"),
				(self.secondary_group_id, "secondary")
			]
			
			for group_id, group_type in unknown_groups:
				try:
					persons = self.face_admin_client.large_person_group.get_persons(group_id)
					
					for person in persons:
						if person.name == person_label:
							# Found the person, update with real name
							timestamp = datetime.now().isoformat()
							updated_user_data = f"{user_data} | Updated from {person_label} to {real_name} at {timestamp}"
							
							self.face_admin_client.large_person_group.update_person(
								group_id,
								person.person_id,
								name=real_name,
								user_data=updated_user_data
							)
							
							self.logger.info(f"Updated person {person_label} to {real_name} in {group_type} unknown group")
							
							# Trigger training to update the model
							self._trigger_training_if_needed(group_id)
							
							return True
							
				except Exception as e:
					self.logger.warning(f"Error searching in {group_type} unknown group: {e}")
					continue
			
			self.logger.warning(f"Person with label '{person_label}' not found in any unknown group")
			return False
			
		except Exception as e:
			self.logger.error(f"Error updating unknown person {person_label}: {e}")
			return False
	
	def get_unknown_persons_list(self) -> list[dict]:
		"""Get list of all unknown persons across both unknown groups"""
		try:
			unknown_persons = []
			unknown_groups = [
				(self.primary_group_id, "primary"),
				(self.secondary_group_id, "secondary")
			]
			
			for group_id, group_type in unknown_groups:
				try:
					persons = self.face_admin_client.large_person_group.get_persons(group_id)
					
					for person in persons:
						# Include all persons from unknown groups
						unknown_persons.append({
							'person_id': person.person_id,
							'name': person.name,
							'user_data': person.user_data or "",
							'group_id': group_id,
							'group_type': group_type,
							'face_count': len(person.persisted_face_ids or [])
						})
						
				except Exception as e:
					self.logger.warning(f"Error getting persons from {group_type} unknown group: {e}")
					continue
			
			return unknown_persons
			
		except Exception as e:
			self.logger.error(f"Error getting unknown persons list: {e}")
			return []
	
	def get_training_status(self) -> Dict[str, Any]:
		"""Get training status for both unknown groups"""
		return {
			'unknown_primary': super().get_training_status(self.primary_group_id),
			'unknown_secondary': super().get_training_status(self.secondary_group_id)
		}
	
	def get_group_statistics(self) -> Dict[str, Any]:
		"""Get statistics for both unknown groups"""
		try:
			primary_persons = self.get_persons_in_group(self.primary_group_id)
			secondary_persons = self.get_persons_in_group(self.secondary_group_id)
			
			# Count faces
			primary_faces = sum(len(p.persisted_face_ids or []) for p in primary_persons)
			secondary_faces = sum(len(p.persisted_face_ids or []) for p in secondary_persons)
			
			training_status = self.get_training_status()
			
			return {
				'unknown_primary_group': {
					'id': self.primary_group_id,
					'persons': len(primary_persons),
					'faces': primary_faces,
					'training': training_status['unknown_primary'],
					'active': self.active_group == "primary"
				},
				'unknown_secondary_group': {
					'id': self.secondary_group_id,
					'persons': len(secondary_persons),
					'faces': secondary_faces,
					'training': training_status['unknown_secondary'],
					'active': self.active_group == "secondary"
				}
			}
			
		except Exception as e:
			self.logger.error(f"Error getting unknown group statistics: {e}")
			return {}
	
	def cleanup_unknown_groups(self) -> bool:
		"""Clean up both unknown groups"""
		try:
			primary_success = self.cleanup_group(self.primary_group_id)
			secondary_success = self.cleanup_group(self.secondary_group_id)
			
			# Reset counter after cleanup
			self.unknown_counter = 0
			
			return primary_success and secondary_success
			
		except Exception as e:
			self.logger.error(f"Error cleaning up unknown groups: {e}")
			return False
