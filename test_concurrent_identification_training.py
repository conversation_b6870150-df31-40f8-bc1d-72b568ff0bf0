# coding: utf-8

"""
Temporary test to demonstrate concurrent identification during training.
This test shows that face identification can be called while training is in progress
using the existing face recognition system.
"""

import os
import time
import threading
import numpy as np
from typing import List, Dict, Any
from pathlib import Path
import sys

# Add the project root to the path for imports
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import the face recognition system
from face_recognition.face_recognition_manager import FaceRecognitionManager

# Mock image data for testing (simulating different people)
def create_mock_face_image(person_id: int, image_id: int) -> np.ndarray:
    """Create mock face image for testing"""
    # Create a unique image pattern for each person/image combination
    # This simulates different face images
    height, width = 200, 200
    image = np.zeros((height, width, 3), dtype=np.uint8)

    # Create unique patterns based on person_id and image_id
    for i in range(height):
        for j in range(width):
            # Create unique RGB values based on person and image
            r = (person_id * 10 + i) % 256
            g = (image_id * 15 + j) % 256
            b = ((person_id + image_id) * 5 + i + j) % 256
            image[i, j] = [r, g, b]

    return image

class ConcurrentIdentificationTest:
    def __init__(self):
        # Use environment variables or defaults for testing
        self.endpoint = os.getenv('AZURE_FACE_API_ENDPOINT', 'https://test-endpoint.cognitiveservices.azure.com/')
        self.key = os.getenv('AZURE_FACE_API_ACCOUNT_KEY', 'test-key-12345')
        self.group_id = "test_concurrent_group"
        self.training_in_progress = False
        self.identification_results = []
        self.face_manager = None

    def setup_face_recognition_system(self):
        """Set up the face recognition system with 20 people"""
        print(f"🔧 Initializing Face Recognition System...")

        # Initialize the face recognition manager
        self.face_manager = FaceRecognitionManager(
            endpoint=self.endpoint,
            api_key=self.key,
            group_id=self.group_id,
            group_name="Concurrent Test Group",
            auto_train_unknown=True,
            max_faces_per_person=5,
            unknown_confidence_threshold=0.8,
            face_similarity_threshold=0.8
        )

        print("✓ Face Recognition Manager initialized")
        return self.face_manager

    def add_people_and_faces(self):
        """Add 20 people with multiple faces each to trigger training"""
        print("👥 Adding 20 people with multiple faces each...")

        person_ids = []

        for person_num in range(1, 21):
            try:
                # Create known person
                person_id = self.face_manager.create_known_person(
                    name=f"Person_{person_num}",
                    user_data=f"Test person number {person_num}"
                )

                if person_id:
                    person_ids.append(person_id)
                    print(f"✓ Created Person_{person_num} (ID: {person_id})")

                    # Add multiple faces per person to make training take longer
                    faces_per_person = 4
                    for face_num in range(1, faces_per_person + 1):
                        try:
                            mock_image = create_mock_face_image(person_num, face_num)
                            face_id = self.face_manager.add_face_to_known_person(
                                person_id=person_id,
                                face_image=mock_image,
                                user_data=f"Face {face_num} for Person_{person_num}"
                            )

                            if face_id:
                                print(f"  ✓ Added face {face_num} for Person_{person_num}")
                            else:
                                print(f"  ✗ Failed to add face {face_num} for Person_{person_num}")

                        except Exception as e:
                            print(f"  ✗ Error adding face {face_num} for Person_{person_num}: {e}")

                else:
                    print(f"✗ Failed to create Person_{person_num}")

            except Exception as e:
                print(f"✗ Error creating Person_{person_num}: {e}")

        print(f"✓ Setup complete! Added {len(person_ids)} people with multiple faces each")
        return person_ids

    def monitor_training_status(self):
        """Monitor training status in background"""
        print("\n🔄 Monitoring training status...")
        self.training_in_progress = True

        def training_monitor():
            try:
                while self.training_in_progress:
                    # Get training status from the face recognition system
                    training_status = self.face_manager.get_training_status()

                    # Check if any training is in progress
                    any_training = False
                    for group_name, status in training_status.items():
                        if isinstance(status, dict) and status.get('training', False):
                            any_training = True
                            print(f"📊 {group_name}: {status.get('status', 'unknown')} - {status.get('message', 'Processing...')}")

                    if not any_training:
                        print("✅ No training currently in progress")
                        # Keep monitoring for a bit longer to catch new training
                        time.sleep(2)
                        # Check one more time
                        training_status = self.face_manager.get_training_status()
                        any_training = any(
                            isinstance(status, dict) and status.get('training', False)
                            for status in training_status.values()
                        )
                        if not any_training:
                            print("✅ Training monitoring completed")
                            self.training_in_progress = False
                            break

                    time.sleep(3)  # Check every 3 seconds

            except Exception as e:
                print(f"❌ Training monitoring error: {e}")
                self.training_in_progress = False

        # Start monitoring in background thread
        monitor_thread = threading.Thread(target=training_monitor, daemon=True)
        monitor_thread.start()
        return monitor_thread

    def attempt_identification_during_training(self, person_ids):
        """Attempt identification while training is in progress"""
        print("\n🔍 Starting identification attempts during training...")

        identification_count = 0
        max_attempts = 15

        def identification_worker():
            nonlocal identification_count

            while self.training_in_progress and identification_count < max_attempts:
                try:
                    print(f"\n🎯 Identification attempt #{identification_count + 1}")

                    # Create a mock frame for identification (simulating video frame)
                    mock_frame = create_mock_face_image(1, 1)  # Use person 1's pattern

                    # Attempt identification using the face recognition system
                    result = self.face_manager.process_frame(
                        frame=mock_frame,
                        confidence_threshold=0.6,
                        return_known_crops=True,
                        auto_train_unknown=True
                    )

                    if result and result.detected_faces:
                        for face in result.detected_faces:
                            if face.is_known:
                                print(f"  ✅ IDENTIFICATION SUCCESS! Person: {face.tag_text}, Confidence: {face.confidence:.3f}")
                                self.identification_results.append({
                                    'attempt': identification_count + 1,
                                    'success': True,
                                    'person_tag': face.tag_text,
                                    'confidence': face.confidence,
                                    'timestamp': time.time()
                                })
                            else:
                                print(f"  ⚠️  Unknown face detected: {face.tag_text}")
                                self.identification_results.append({
                                    'attempt': identification_count + 1,
                                    'success': False,
                                    'reason': 'Unknown face',
                                    'person_tag': face.tag_text,
                                    'timestamp': time.time()
                                })
                    else:
                        print(f"  ⚠️  No faces detected in frame")
                        self.identification_results.append({
                            'attempt': identification_count + 1,
                            'success': False,
                            'reason': 'No faces detected',
                            'timestamp': time.time()
                        })

                except Exception as e:
                    print(f"  ❌ Identification error: {e}")
                    self.identification_results.append({
                        'attempt': identification_count + 1,
                        'success': False,
                        'error': str(e),
                        'timestamp': time.time()
                    })

                identification_count += 1
                time.sleep(2)  # Wait between attempts

            print(f"\n📈 Identification attempts completed: {identification_count}")

        # Start identification in background thread
        identification_thread = threading.Thread(target=identification_worker, daemon=True)
        identification_thread.start()
        return identification_thread

    async def cleanup(self, face_admin_client):
        """Clean up the test group"""
        try:
            print(f"\n🧹 Cleaning up test group: {self.large_person_group_id}")
            await face_admin_client.large_person_group.delete(self.large_person_group_id)
            print("✓ Cleanup completed")
        except Exception as e:
            print(f"Cleanup error: {e}")

    async def run_concurrent_test(self):
        """Main test function that demonstrates concurrent identification during training"""
        print("🚀 Starting Concurrent Identification During Training Test")
        print("=" * 60)
        
        try:
            # Setup
            person_ids, face_admin_client, face_client = await self.setup_large_person_group_with_many_people()
            
            # Start training and identification concurrently
            training_task = asyncio.create_task(self.start_training_async(face_admin_client))
            
            # Wait a moment for training to start
            await asyncio.sleep(3)
            
            identification_task = asyncio.create_task(
                self.attempt_identification_during_training(face_client, person_ids)
            )
            
            # Wait for both to complete
            await asyncio.gather(training_task, identification_task)
            
            # Print results
            self.print_test_results()
            
            # Cleanup
            await self.cleanup(face_admin_client)
            
        except Exception as e:
            print(f"❌ Test error: {e}")

    def print_test_results(self):
        """Print the test results summary"""
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        total_attempts = len(self.identification_results)
        successful_attempts = sum(1 for r in self.identification_results if r.get('success', False))
        
        print(f"Total identification attempts: {total_attempts}")
        print(f"Successful identifications: {successful_attempts}")
        print(f"Success rate: {(successful_attempts/total_attempts*100):.1f}%" if total_attempts > 0 else "No attempts")
        
        if successful_attempts > 0:
            print("\n✅ CONCLUSION: Identification CAN be performed during training!")
            print("   The quad-group architecture allows continuous identification capability.")
        else:
            print("\n⚠️  No successful identifications during training period.")
            print("   This may be due to mock data or timing issues.")
            
        print("\nDetailed results:")
        for result in self.identification_results:
            status = "✅" if result.get('success') else "❌"
            print(f"  {status} Attempt {result['attempt']}: {result}")

async def main():
    """Run the concurrent identification test"""
    test = ConcurrentIdentificationTest()
    await test.run_concurrent_test()

if __name__ == "__main__":
    print("Testing concurrent identification during training...")
    print("This demonstrates that the face recognition system can identify faces")
    print("even while training is in progress using the quad-group architecture.")
    print()
    
    asyncio.run(main())
