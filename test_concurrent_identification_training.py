# coding: utf-8

"""
Real test to demonstrate concurrent identification during training.
This test uses actual images and Azure API credentials to show that face identification
can be called while training is in progress using the quad-group architecture.
"""

import os
import time
import threading
import cv2
import numpy as np
from typing import List, Dict, Any
from pathlib import Path
import sys
from dotenv import load_dotenv

# Add the project root to the path for imports
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import the face recognition system and shared utilities
from face_recognition.face_recognition_manager import FaceRecognitionManager
from shared.constants import TestImages, CONFIGURATION_NAME_FACE_API_ENDPOINT, CONFIGURATION_NAME_FACE_API_ACCOUNT_KEY
from shared.helpers import get_image_path, read_file_content

def load_image_as_numpy(image_path: Path) -> np.ndarray:
    """Load image file as numpy array"""
    try:
        # Read image using OpenCV
        image = cv2.imread(str(image_path))
        if image is not None:
            # Convert BGR to RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            return image
        else:
            print(f"Warning: Could not load image {image_path}")
            return None
    except Exception as e:
        print(f"Error loading image {image_path}: {e}")
        return None

class ConcurrentIdentificationTest:
    def __init__(self):
        # Load environment variables
        load_dotenv()

        # Get Azure credentials from environment
        self.endpoint = os.getenv(CONFIGURATION_NAME_FACE_API_ENDPOINT)
        self.key = os.getenv(CONFIGURATION_NAME_FACE_API_ACCOUNT_KEY)

        if not self.endpoint or not self.key:
            raise ValueError("Please set AZURE_FACE_API_ENDPOINT and AZURE_FACE_API_ACCOUNT_KEY in your .env file")

        self.group_id = "test_concurrent_group"
        self.training_in_progress = False
        self.training_start_time = None
        self.identification_results = []
        self.face_manager = None
        self.person_images = self._get_available_person_images()

    def _get_available_person_images(self) -> Dict[str, List[str]]:
        """Get available person images organized by person"""
        person_images = {
            "Dad": [TestImages.IMAGE_FAMILY_1_DAD_1, TestImages.IMAGE_FAMILY_1_DAD_2, TestImages.IMAGE_FAMILY_1_DAD_3],
            "Mom": [TestImages.IMAGE_FAMILY_1_MOM_1, TestImages.IMAGE_FAMILY_1_MOM_2],
            "Son": [TestImages.IMAGE_FAMILY_1_SON_1, TestImages.IMAGE_FAMILY_1_SON_2],
            "Daughter": [TestImages.IMAGE_FAMILY_1_DAUGHTER_1, TestImages.IMAGE_FAMILY_1_DAUGHTER_2, TestImages.IMAGE_FAMILY_1_DAUGHTER_3],
            "Family2_Lady": [TestImages.IMAGE_FAMILY_2_LADY_1, TestImages.IMAGE_FAMILY_2_LADY_2],
            "Family2_Man": [TestImages.IMAGE_FAMILY_2_Man_1, TestImages.IMAGE_FAMILY_2_Man_2],
            "Family3_Lady": [TestImages.IMAGE_FAMILY_3_LADY_1],
            "Family3_Man": [TestImages.IMAGE_FAMILY_3_Man_1],
            "Child1": [TestImages.IMAGE_CHILD_1_PERSON_GROUP],
            "Child2": [TestImages.IMAGE_CHILD_2_PERSON_GROUP],
            "Child3": [TestImages.IMAGE_CHILD_3_PERSON_GROUP],
            "Man1": [TestImages.IMAGE_MAN1_PERSON_GROUP],
            "Man2": [TestImages.IMAGE_MAN2_PERSON_GROUP],
            "Man3": [TestImages.IMAGE_MAN3_PERSON_GROUP],
            "Woman1": [TestImages.IMAGE_WOMAN1_PERSON_GROUP],
            "Woman2": [TestImages.IMAGE_WOMAN2_PERSON_GROUP],
            "Woman3": [TestImages.IMAGE_WOMAN3_PERSON_GROUP],
        }
        return person_images

    def setup_face_recognition_system(self):
        """Set up the face recognition system with 20 people"""
        print(f"🔧 Initializing Face Recognition System...")

        # Initialize the face recognition manager
        self.face_manager = FaceRecognitionManager(
            endpoint=self.endpoint,
            api_key=self.key,
            group_id=self.group_id,
            group_name="Concurrent Test Group",
            auto_train_unknown=True,
            max_faces_per_person=5,
            unknown_confidence_threshold=0.8,
            face_similarity_threshold=0.8
        )

        print("✓ Face Recognition Manager initialized")
        return self.face_manager

    def add_people_and_faces(self):
        """Add people with real face images to trigger training"""
        print(f"👥 Adding {len(self.person_images)} people with real face images...")

        person_ids = []

        for person_name, image_files in self.person_images.items():
            try:
                # Create known person
                person_id = self.face_manager.create_known_person(
                    name=person_name,
                    user_data=f"Real person: {person_name}"
                )

                if person_id:
                    person_ids.append(person_id)
                    print(f"✓ Created {person_name} (ID: {person_id})")

                    # Add all available face images for this person
                    for face_num, image_file in enumerate(image_files, 1):
                        try:
                            # Load real image
                            image_path = get_image_path(image_file)
                            face_image = load_image_as_numpy(image_path)

                            if face_image is not None:
                                face_id = self.face_manager.add_face_to_known_person(
                                    person_id=person_id,
                                    face_image=face_image,
                                    user_data=f"Face {face_num} for {person_name} from {image_file}"
                                )

                                if face_id:
                                    print(f"  ✓ Added face {face_num} for {person_name} from {image_file}")
                                else:
                                    print(f"  ✗ Failed to add face {face_num} for {person_name}")
                            else:
                                print(f"  ✗ Could not load image {image_file} for {person_name}")

                        except Exception as e:
                            print(f"  ✗ Error adding face {face_num} for {person_name}: {e}")

                else:
                    print(f"✗ Failed to create {person_name}")

            except Exception as e:
                print(f"✗ Error creating {person_name}: {e}")

        print(f"✓ Setup complete! Added {len(person_ids)} people with real face images")

        # FIRST TRAINING: Quick training with current data
        print("\n🚀 PHASE 1: Initial training with current data...")
        self.train_and_wait_for_completion()

        # IDENTIFICATION AFTER FIRST TRAINING
        print("\n🔍 PHASE 1.5: Testing identification after first training...")
        self.test_identification_after_first_training()

        # SECOND PHASE: Add same images with different names to increase training data
        print("\n🚀 PHASE 2: Adding duplicate images with different names to increase training time...")
        self.add_duplicate_images_with_different_names()

        # SECOND TRAINING: This will take longer due to more data
        print("\n🚀 PHASE 3: Starting longer training with increased data...")
        self.start_longer_training()

        return person_ids

    def train_and_wait_for_completion(self):
        """Phase 1: Train current data and wait for completion"""
        try:
            known_groups = self.face_manager.group_manager.known_groups
            primary_group_id = known_groups.primary_group_id

            print(f"⏱️  Starting initial training for: {primary_group_id}")
            start_time = time.time()

            # Start training
            known_groups.train_group_async(primary_group_id, "known_primary")

            # Wait for training to complete
            while True:
                try:
                    status = known_groups.get_training_status()
                    primary_status = status.get('known_primary', {})

                    if isinstance(primary_status, dict):
                        training_status = primary_status.get('status', 'unknown')
                        if training_status == 'succeeded':
                            end_time = time.time()
                            training_time = end_time - start_time
                            print(f"✅ Initial training completed in {training_time:.2f} seconds")
                            break
                        elif training_status == 'failed':
                            print(f"❌ Initial training failed")
                            break

                    time.sleep(2)

                except Exception as e:
                    print(f"⚠️  Error checking training status: {e}")
                    time.sleep(2)

        except Exception as e:
            print(f"❌ Error in initial training: {e}")

    def test_identification_after_first_training(self):
        """Test identification after first training completes"""
        try:
            print("🎯 Testing identification with identification1.jpg after first training...")

            # Load the test image
            test_image_file = "identification1.jpg"
            image_path = get_image_path(test_image_file)
            test_image = load_image_as_numpy(image_path)

            if test_image is not None:
                print(f"  📷 Loaded test image: {test_image_file}")

                # Process the frame for identification
                result = self.face_manager.process_frame(test_image)

                if result and result.identified_faces:
                    print(f"  ✅ SUCCESS: Identified {len(result.identified_faces)} face(s)")
                    for face in result.identified_faces:
                        confidence = face.confidence if hasattr(face, 'confidence') else 'N/A'
                        print(f"    👤 {face.person_name} (confidence: {confidence})")
                else:
                    print(f"  ⚠️  No faces identified in {test_image_file}")
                    if result and result.detected_faces:
                        print(f"    📊 Detected {len(result.detected_faces)} face(s) but none identified")
                    else:
                        print(f"    📊 No faces detected at all")

            else:
                print(f"  ❌ Failed to load test image: {test_image_file}")

        except Exception as e:
            print(f"❌ Error in identification after first training: {e}")

    def add_duplicate_images_with_different_names(self):
        """Phase 2: Add same images with different names to increase training data"""
        try:
            print("📸 Adding duplicate images with different names...")

            # Create new people with "_v2" suffix and add same images
            duplicate_count = 0
            for person_name, image_files in self.person_images.items():
                try:
                    # Create duplicate person with different name
                    duplicate_name = f"{person_name}_v2"
                    person_id = self.face_manager.create_known_person(duplicate_name, f"Duplicate of {person_name}")

                    if person_id:
                        print(f"  ✓ Created duplicate person: {duplicate_name} (ID: {person_id})")

                        # Add same images to this duplicate person
                        for face_num, image_file in enumerate(image_files, 1):
                            try:
                                image_path = get_image_path(image_file)
                                face_image = load_image_as_numpy(image_path)

                                if face_image is not None:
                                    face_id = self.face_manager.add_face_to_known_person(
                                        person_id=person_id,
                                        face_image=face_image,
                                        user_data=f"Duplicate face {face_num} for {duplicate_name} from {image_file}"
                                    )

                                    if face_id:
                                        duplicate_count += 1
                                        print(f"    ✓ Added duplicate face {face_num} from {image_file}")

                            except Exception as e:
                                print(f"    ✗ Error adding duplicate face: {e}")

                    else:
                        print(f"  ✗ Failed to create duplicate person: {duplicate_name}")

                except Exception as e:
                    print(f"  ✗ Error creating duplicate for {person_name}: {e}")

            print(f"✅ Added {duplicate_count} duplicate face images to increase training data")

        except Exception as e:
            print(f"❌ Error adding duplicate images: {e}")

    def start_longer_training(self):
        """Phase 3: Start training with increased data (will take longer)"""
        try:
            known_groups = self.face_manager.group_manager.known_groups
            primary_group_id = known_groups.primary_group_id

            print(f"⏱️  Starting longer training with increased data for: {primary_group_id}")
            self.training_start_time = time.time()
            self.training_in_progress = True

            # Start training
            known_groups.train_group_async(primary_group_id, "known_primary")
            print("✅ Longer training started - this should take more time due to increased data")

        except Exception as e:
            print(f"❌ Error starting longer training: {e}")
            self.training_in_progress = False

    def monitor_training_status(self):
        """Monitor longer training status and track total training time"""
        print("\n🔄 Monitoring longer training status...")

        def training_monitor():
            try:
                while self.training_in_progress:
                    # Get training status from the face recognition system
                    training_status = self.face_manager.get_training_status()

                    # Check primary group training status
                    primary_status = training_status.get('known_primary', {})

                    if isinstance(primary_status, dict):
                        api_status = primary_status.get('status', 'unknown')

                        # Calculate elapsed time
                        if hasattr(self, 'training_start_time'):
                            elapsed = time.time() - self.training_start_time
                            print(f"⏱️  Training in progress... Elapsed: {elapsed:.1f}s, Status: {api_status}")

                        # Check if training completed
                        if api_status == 'succeeded':
                            total_time = time.time() - self.training_start_time
                            print(f"✅ LONGER TRAINING COMPLETED! Total time: {total_time:.2f} seconds")
                            self.training_in_progress = False
                            break
                        elif api_status == 'failed':
                            total_time = time.time() - self.training_start_time
                            print(f"❌ Training failed after {total_time:.2f} seconds")
                            self.training_in_progress = False
                            break

                    time.sleep(3)  # Check every 3 seconds

            except Exception as e:
                print(f"❌ Training monitoring error: {e}")
                self.training_in_progress = False

        # Start monitoring in background thread
        monitor_thread = threading.Thread(target=training_monitor, daemon=True)
        monitor_thread.start()
        return monitor_thread

    def attempt_identification_during_training(self):
        """Attempt identification while training is in progress using real test images"""
        print("\n🔍 Starting identification attempts during training...")

        identification_count = 0
        max_attempts = 15

        # Get some test images for identification
        test_images = [
            TestImages.IMAGE_IDENTIFICATION1,
            TestImages.IMAGE_DETECTION_1,
            TestImages.IMAGE_DETECTION_2,
            TestImages.IMAGE_DETECTION_3,
            TestImages.TEST_IMAGE_PERSON_GROUP,
            TestImages.IMAGE_FINDSIMILAR
        ]

        def identification_worker():
            nonlocal identification_count

            while self.training_in_progress and identification_count < max_attempts:
                try:
                    print(f"\n🎯 Identification attempt #{identification_count + 1}")

                    # Use a different test image for each attempt
                    test_image_file = test_images[identification_count % len(test_images)]
                    image_path = get_image_path(test_image_file)
                    test_frame = load_image_as_numpy(image_path)

                    if test_frame is not None:
                        print(f"  📷 Using test image: {test_image_file}")

                        # Attempt identification using the face recognition system
                        result = self.face_manager.process_frame(
                            frame=test_frame,
                            confidence_threshold=0.6,
                            return_known_crops=True,
                            auto_train_unknown=True
                        )

                        if result and result.detected_faces:
                            for face in result.detected_faces:
                                if face.is_known:
                                    print(f"  ✅ IDENTIFICATION SUCCESS! Person: {face.tag_text}, Confidence: {face.confidence:.3f}")
                                    self.identification_results.append({
                                        'attempt': identification_count + 1,
                                        'success': True,
                                        'person_tag': face.tag_text,
                                        'confidence': face.confidence,
                                        'test_image': test_image_file,
                                        'timestamp': time.time()
                                    })
                                else:
                                    print(f"  ⚠️  Unknown face detected: {face.tag_text}")
                                    self.identification_results.append({
                                        'attempt': identification_count + 1,
                                        'success': False,
                                        'reason': 'Unknown face',
                                        'person_tag': face.tag_text,
                                        'test_image': test_image_file,
                                        'timestamp': time.time()
                                    })
                        else:
                            print(f"  ⚠️  No faces detected in frame")
                            self.identification_results.append({
                                'attempt': identification_count + 1,
                                'success': False,
                                'reason': 'No faces detected',
                                'test_image': test_image_file,
                                'timestamp': time.time()
                            })
                    else:
                        print(f"  ❌ Could not load test image: {test_image_file}")
                        self.identification_results.append({
                            'attempt': identification_count + 1,
                            'success': False,
                            'error': f'Could not load image {test_image_file}',
                            'timestamp': time.time()
                        })

                except Exception as e:
                    print(f"  ❌ Identification error: {e}")
                    self.identification_results.append({
                        'attempt': identification_count + 1,
                        'success': False,
                        'error': str(e),
                        'timestamp': time.time()
                    })

                identification_count += 1
                time.sleep(3)  # Wait between attempts

            print(f"\n📈 Identification attempts completed: {identification_count}")

        # Start identification in background thread
        identification_thread = threading.Thread(target=identification_worker, daemon=True)
        identification_thread.start()
        return identification_thread

    def cleanup(self):
        """Clean up the test group"""
        try:
            print(f"\n🧹 Cleaning up test group: {self.group_id}")
            if self.face_manager:
                # The face manager will handle cleanup when it's destroyed
                print("✓ Face manager will handle cleanup")
        except Exception as e:
            print(f"Cleanup error: {e}")

    def test_identification_after_second_training(self):
        """Test identification after second training completes"""
        try:
            print("🎯 Testing identification with identification1.jpg after second training...")

            # Load the test image
            test_image_file = "identification1.jpg"
            image_path = get_image_path(test_image_file)
            test_image = load_image_as_numpy(image_path)

            if test_image is not None:
                print(f"  📷 Loaded test image: {test_image_file}")

                # Process the frame for identification
                result = self.face_manager.process_frame(test_image)

                if result and result.identified_faces:
                    print(f"  ✅ SUCCESS: Identified {len(result.identified_faces)} face(s)")
                    for face in result.identified_faces:
                        confidence = face.confidence if hasattr(face, 'confidence') else 'N/A'
                        print(f"    👤 {face.person_name} (confidence: {confidence})")
                else:
                    print(f"  ⚠️  No faces identified in {test_image_file}")
                    if result and result.detected_faces:
                        print(f"    📊 Detected {len(result.detected_faces)} face(s) but none identified")
                    else:
                        print(f"    📊 No faces detected at all")

            else:
                print(f"  ❌ Failed to load test image: {test_image_file}")

        except Exception as e:
            print(f"❌ Error in identification after second training: {e}")

    def run_concurrent_test(self):
        """Main test function that demonstrates concurrent identification during training"""
        print("🚀 Starting Concurrent Identification During Training Test")
        print("=" * 60)

        try:
            # Setup face recognition system
            self.setup_face_recognition_system()

            # Add people and faces (this will trigger training)
            person_ids = self.add_people_and_faces()

            if not person_ids:
                print("❌ No people were added successfully. Cannot proceed with test.")
                return

            # Start identification attempts during the longer training
            identification_thread = self.attempt_identification_during_training()

            # Start monitoring the longer training status
            training_monitor_thread = self.monitor_training_status()

            # Wait for both threads to complete
            print("\n⏳ Waiting for longer training and identification to complete...")
            training_monitor_thread.join(timeout=300)  # Wait up to 5 minutes for longer training
            identification_thread.join(timeout=300)

            # IDENTIFICATION AFTER SECOND TRAINING
            print("\n🔍 PHASE 4: Testing identification after second training completes...")
            self.test_identification_after_second_training()

            # Print results
            self.print_test_results()

            # Cleanup
            self.cleanup()

        except Exception as e:
            print(f"❌ Test error: {e}")
            import traceback
            traceback.print_exc()

    def print_test_results(self):
        """Print the test results summary"""
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        total_attempts = len(self.identification_results)
        successful_attempts = sum(1 for r in self.identification_results if r.get('success', False))
        
        print(f"Total identification attempts: {total_attempts}")
        print(f"Successful identifications: {successful_attempts}")
        print(f"Success rate: {(successful_attempts/total_attempts*100):.1f}%" if total_attempts > 0 else "No attempts")
        
        if successful_attempts > 0:
            print("\n✅ CONCLUSION: Identification CAN be performed during training!")
            print("   The quad-group architecture allows continuous identification capability.")
        else:
            print("\n⚠️  No successful identifications during training period.")
            print("   This may be due to mock data or timing issues.")
            
        print("\nDetailed results:")
        for result in self.identification_results:
            status = "✅" if result.get('success') else "❌"
            print(f"  {status} Attempt {result['attempt']}: {result}")

def main():
    """Run the concurrent identification test"""
    test = ConcurrentIdentificationTest()
    test.run_concurrent_test()

if __name__ == "__main__":
    print("Testing concurrent identification during training...")
    print("This demonstrates that the face recognition system can identify faces")
    print("even while training is in progress using the quad-group architecture.")
    print("Using real images from the images folder and Azure API credentials from .env")
    print()

    main()
