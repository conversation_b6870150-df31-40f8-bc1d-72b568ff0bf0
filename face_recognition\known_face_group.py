# coding: utf-8

"""
FILE: known_face_group.py

DESCRIPTION:
    Known face group management class that handles operations for known persons.
    Manages primary/secondary known groups with auto-face addition capabilities.

FEATURES:
    - Dual known group management (primary/secondary)
    - Auto-face addition to known persons
    - Known person identification
    - Training coordination between groups
    - Active group switching
"""

import numpy as np
from typing import List, Dict, Any, Optional
from .base_group_manager import BaseGroupManager


class KnownFaceGroup(BaseGroupManager):
	"""Manages known face groups with dual primary/secondary architecture"""
	
	def __init__(
		self,
		endpoint: str,
		api_key: str,
		group_id: str,
		group_name: str,
		max_faces_per_person: int = 10,
		face_similarity_threshold: float = 0.8,
		save_known_images: bool = False,
		logger: Optional[Any] = None
	):
		"""Initialize known face group manager"""
		super().__init__(endpoint, api_key, max_faces_per_person, face_similarity_threshold, logger)

		self.base_group_id = group_id
		self.base_group_name = group_name
		self.save_known_images = save_known_images

		# Known group IDs
		self.primary_group_id = f"{group_id}_known_primary"
		self.secondary_group_id = f"{group_id}_known_secondary"

		# Training states for known groups
		self.primary_training = False
		self.secondary_training = False

		# Active group tracking
		self.active_group = "primary"  # primary or secondary

		# Initialize groups
		self._initialize_groups()
	
	def _initialize_groups(self):
		"""Initialize both known groups"""
		primary_name = f"{self.base_group_name} - Known Primary"
		secondary_name = f"{self.base_group_name} - Known Secondary"
		
		self.create_group_if_not_exists(self.primary_group_id, primary_name)
		self.create_group_if_not_exists(self.secondary_group_id, secondary_name)
		
		self.logger.info(f"Initialized known groups: {self.primary_group_id}, {self.secondary_group_id}")
	
	def get_active_group_id(self) -> str:
		"""Get the currently active known group ID"""
		return self.primary_group_id if self.active_group == "primary" else self.secondary_group_id
	
	def get_standby_group_id(self) -> str:
		"""Get the standby known group ID"""
		return self.secondary_group_id if self.active_group == "primary" else self.primary_group_id
	
	def switch_active_group(self):
		"""Switch active group (used when training starts)"""
		old_active = self.active_group
		self.active_group = "secondary" if self.active_group == "primary" else "primary"
		new_active = self.active_group
		
		self.logger.info(f"Switched known active group: {old_active} → {new_active}")
	
	def identify_faces(self, face_ids: List[str], confidence_threshold: float = 0.6) -> List[Any]:
		"""Identify faces against active known group"""
		try:
			active_group_id = self.get_active_group_id()
			
			identification_results = self.face_client.identify(
				face_ids=face_ids,
				large_person_group_id=active_group_id
			)
			
			self.logger.debug(f"Known identification completed against {active_group_id}")
			return identification_results
			
		except Exception as e:
			self.logger.error(f"Error in known face identification: {e}")
			return []
	
	def add_face_to_known_person_if_needed(self, person_id: str, frame: np.ndarray, 
										   detected_face, confidence: float) -> bool:
		"""Add face to known person if conditions are met"""
		try:
			# Check if conditions are met for auto-addition
			if confidence < 0.8:
				return False
			
			active_group_id = self.get_active_group_id()
			
			# Get person details to check face count
			person = self.face_admin_client.large_person_group.get_person(active_group_id, person_id)
			current_face_count = len(person.persisted_face_ids or [])
			
			if current_face_count >= self.max_faces_per_person:
				return False
			
			# Crop face from frame
			face_image = self._crop_face_from_frame(frame, detected_face)
			if face_image is None:
				return False
			
			# Add face with descriptive user_data
			face_id = self.add_face_to_person_in_group(
				active_group_id,
				person_id,
				face_image,
				f"Auto-added from video frame (confidence: {confidence:.3f})"
			)
			
			if face_id:
				# Save known face image if enabled (only when training)
				if self.save_known_images:
					# Get person details for name
					try:
						person = self.face_admin_client.large_person_group.get_person(active_group_id, person_id)
						person_name = person.name or "Unknown"
						self.save_known_face_image(face_image, person_id, face_id, person_name, "")
					except Exception as e:
						self.logger.error(f"Error saving known face image: {e}")

				self.logger.info(f"Auto-added face to known person {person_id} (confidence: {confidence:.3f})")
				# Trigger training for the group
				self._trigger_training_if_needed(active_group_id)
				return True
			
			return False
			
		except Exception as e:
			self.logger.error(f"Error auto-adding face to known person: {e}")
			return False
	
	def _trigger_training_if_needed(self, group_id: str):
		"""Trigger smart training for known group if not already training"""
		try:
			# Determine which group and check if training
			if group_id == self.primary_group_id:
				if not self.primary_training:
					self.primary_training = True
					# Switch to secondary group for identification
					if self.active_group == "primary":
						self.switch_active_group()

					# Add to smart training queue with callback to train secondary
					def after_primary_training():
						self.primary_training = False
						# Queue secondary group for training if it has faces
						if not self.secondary_training:
							self._queue_secondary_training()

					self.add_to_training_queue(group_id, "known_primary", after_primary_training)

			elif group_id == self.secondary_group_id:
				if not self.secondary_training:
					self.secondary_training = True
					# Switch to primary group for identification
					if self.active_group == "secondary":
						self.switch_active_group()

					# Add to smart training queue with callback to train primary
					def after_secondary_training():
						self.secondary_training = False
						# Queue primary group for training if it has faces
						if not self.primary_training:
							self._queue_primary_training()

					self.add_to_training_queue(group_id, "known_secondary", after_secondary_training)

		except Exception as e:
			self.logger.error(f"Error triggering smart training for known group {group_id}: {e}")

	def _queue_primary_training(self):
		"""Queue primary group for training if it needs training"""
		try:
			# Check if primary group has enough faces to warrant training
			persons = self.face_admin_client.large_person_group.list_persons(self.primary_group_id)
			total_faces = sum(len(person.persisted_face_ids or []) for person in persons)

			if total_faces > 0:  # Has faces to train
				self.logger.info(f"Queueing primary known group for training ({total_faces} faces)")

				def after_training():
					self.primary_training = False

				self.add_to_training_queue(self.primary_group_id, "known_primary", after_training)
		except Exception as e:
			self.logger.error(f"Error queueing primary training: {e}")

	def _queue_secondary_training(self):
		"""Queue secondary group for training if it needs training"""
		try:
			# Check if secondary group has enough faces to warrant training
			persons = self.face_admin_client.large_person_group.list_persons(self.secondary_group_id)
			total_faces = sum(len(person.persisted_face_ids or []) for person in persons)

			if total_faces > 0:  # Has faces to train
				self.logger.info(f"Queueing secondary known group for training ({total_faces} faces)")

				def after_training():
					self.secondary_training = False

				self.add_to_training_queue(self.secondary_group_id, "known_secondary", after_training)
		except Exception as e:
			self.logger.error(f"Error queueing secondary training: {e}")
	
	def train_group_async(self, group_id: str, group_type: str):
		"""Start asynchronous training for known group (legacy method)"""
		# Use the new smart training system
		def after_training():
			if group_type == "known_primary":
				self.primary_training = False
			elif group_type == "known_secondary":
				self.secondary_training = False

		self.add_to_training_queue(group_id, group_type, after_training)
	
	def get_training_status(self) -> Dict[str, Any]:
		"""Get training status for both known groups"""
		return {
			'known_primary': super().get_training_status(self.primary_group_id),
			'known_secondary': super().get_training_status(self.secondary_group_id)
		}
	
	def get_group_statistics(self) -> Dict[str, Any]:
		"""Get statistics for both known groups"""
		try:
			primary_persons = self.get_persons_in_group(self.primary_group_id)
			secondary_persons = self.get_persons_in_group(self.secondary_group_id)
			
			# Count faces
			primary_faces = sum(len(p.persisted_face_ids or []) for p in primary_persons)
			secondary_faces = sum(len(p.persisted_face_ids or []) for p in secondary_persons)
			
			training_status = self.get_training_status()
			
			return {
				'known_primary_group': {
					'id': self.primary_group_id,
					'persons': len(primary_persons),
					'faces': primary_faces,
					'training': training_status['known_primary'],
					'active': self.active_group == "primary"
				},
				'known_secondary_group': {
					'id': self.secondary_group_id,
					'persons': len(secondary_persons),
					'faces': secondary_faces,
					'training': training_status['known_secondary'],
					'active': self.active_group == "secondary"
				}
			}
			
		except Exception as e:
			self.logger.error(f"Error getting known group statistics: {e}")
			return {}
	
	def create_known_person(self, name: str, user_data: str = "") -> str | None:
		"""Create a person in the active known group"""
		active_group_id = self.get_active_group_id()
		return self.create_person_in_group(active_group_id, name, user_data)
	
	def add_face_to_known_person(self, person_id: str, face_image: np.ndarray, user_data: str = "") -> str | None:
		"""Add face to known person in active group"""
		active_group_id = self.get_active_group_id()
		return self.add_face_to_person_in_group(active_group_id, person_id, face_image, user_data)
	
	def get_known_persons(self) -> List[Any]:
		"""Get all persons from both known groups"""
		primary_persons = self.get_persons_in_group(self.primary_group_id)
		secondary_persons = self.get_persons_in_group(self.secondary_group_id)
		
		# Add group info to persons
		for person in primary_persons:
			person.group_type = "primary"
			person.group_id = self.primary_group_id
		
		for person in secondary_persons:
			person.group_type = "secondary"
			person.group_id = self.secondary_group_id
		
		return primary_persons + secondary_persons
	
	def cleanup_known_groups(self) -> bool:
		"""Clean up both known groups"""
		try:
			primary_success = self.cleanup_group(self.primary_group_id)
			secondary_success = self.cleanup_group(self.secondary_group_id)
			
			return primary_success and secondary_success
			
		except Exception as e:
			self.logger.error(f"Error cleaning up known groups: {e}")
			return False
