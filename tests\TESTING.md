# Face Recognition Testing Documentation

## Overview

This document describes the testing strategy and implementation for the face recognition system. The test suite provides comprehensive coverage of all public methods with proper documentation and automatic cleanup functionality.

## Test Structure

### Test Files
- `test_face_recognition.py` - Main test suite with all unit tests
- `run_tests.py` - Professional test runner with multiple options
- `TESTING.md` - This documentation file

### Test Coverage

#### ✅ FaceRecognitionManager (15 methods tested)
- `test_initialization_default_parameters` - Tests default parameter initialization
- `test_initialization_custom_parameters` - Tests custom parameter initialization
- `test_setup_logger` - Tests logger configuration
- `test_process_frame` - Tests frame processing delegation
- `test_create_known_person` - Tests known person creation
- `test_add_face_to_known_person` - Tests face addition to known person
- `test_get_known_persons` - Tests known persons retrieval
- `test_update_unknown_person_with_real_name` - Tests unknown person updates
- `test_get_unknown_persons_list` - Tests unknown persons listing
- `test_get_group_statistics` - Tests statistics retrieval
- `test_get_training_status` - Tests training status checking
- `test_get_active_known_group_id` - Tests active known group ID
- `test_get_active_unknown_group_id` - Tests active unknown group ID
- `test_cleanup_all_groups` - Tests group cleanup functionality
- `test_save_cropped_faces` - Tests face image saving
- `test_string_representation` - Tests string representation methods

#### ✅ FrameProcessingResult (2 tests)
- `test_frame_processing_result_creation` - Tests result creation with faces
- `test_frame_processing_result_empty` - Tests empty result creation

#### ✅ DetectedFace (2 tests)
- `test_detected_face_creation_known` - Tests known face detection
- `test_detected_face_creation_unknown` - Tests unknown face detection

#### ✅ FaceGroupManager (3 tests)
- `test_initialization` - Tests manager initialization
- `test_process_frame_no_faces` - Tests frame processing with no faces
- `test_image_to_bytes` - Tests image conversion utility

#### ✅ BaseGroupManager (3 tests)
- `test_initialization` - Tests base manager initialization
- `test_create_group_if_not_exists_new_group` - Tests new group creation
- `test_create_group_if_not_exists_existing_group` - Tests existing group handling

#### ✅ Cleanup Functionality (1 test)
- `test_cleanup_all_groups_success` - Tests successful cleanup operation

## Running Tests

### Basic Usage

```bash
# Run all tests
python tests/run_tests.py

# Run with verbose output (default)
python tests/run_tests.py --verbose

# Run with coverage report
python tests/run_tests.py --coverage
```

### Advanced Usage

```bash
# Run specific test categories
python tests/run_tests.py --unit
python tests/run_tests.py --integration

# Run specific test file
python tests/run_tests.py --file tests/test_face_recognition.py

# Run specific test class
python tests/run_tests.py --class TestFaceRecognitionManager

# Run specific test function
python tests/run_tests.py --class TestFaceRecognitionManager --function test_initialization_default_parameters
```

### Direct pytest Usage

```bash
# Run all tests with pytest directly
uv run pytest tests/test_face_recognition.py -v

# Run with coverage
uv run pytest tests/test_face_recognition.py --cov=face_recognition --cov-report=html -v

# Run specific test class
uv run pytest tests/test_face_recognition.py::TestFaceRecognitionManager -v

# Run specific test method
uv run pytest tests/test_face_recognition.py::TestFaceRecognitionManager::test_initialization_default_parameters -v
```

## Test Structure

```
tests/
├── test_face_recognition.py    # Main test file with comprehensive tests
├── run_tests.py               # Test runner script
├── TESTING.md                 # This documentation
└── __init__.py                # Package initialization

pytest.ini                    # Pytest configuration (project root)
```

## Running Tests

### Quick Start

```bash
# Install test dependencies
uv add --optional-group test

# Run all tests
python tests/run_tests.py

# Or use pytest directly
uv run pytest tests/test_face_recognition.py -v
```

### Test Categories

```bash
# Unit tests only
python tests/run_tests.py --unit

# Integration tests only
python tests/run_tests.py --integration

# With coverage report
python tests/run_tests.py --coverage

# Verbose output
python tests/run_tests.py --verbose
```

### Specific Tests

```bash
# Run specific test file
python tests/run_tests.py --file tests/test_face_recognition.py

# Run specific test class
python tests/run_tests.py --file tests/test_face_recognition.py --class TestFaceRecognitionManager

# Run specific test function
python tests/run_tests.py --file tests/test_face_recognition.py --class TestFaceRecognitionManager --function test_initialization
```

## Test Coverage

The tests cover:

### ✅ FaceRecognitionManager
- Initialization with various parameters
- Known person creation and management
- Face addition to known persons
- Unknown person management and updates
- Frame processing
- Statistics and monitoring
- Error handling

### ✅ Data Classes
- FrameProcessingResult creation and validation
- DetectedFace creation for known and unknown faces
- Proper data structure validation

### ✅ Integration Tests
- Complete workflow simulation
- Azure API mocking
- End-to-end functionality

### ✅ Error Handling
- Invalid credentials
- None/empty inputs
- API failure scenarios

## Test Dependencies

The tests use mocking to avoid requiring actual Azure Face API credentials:

- `unittest.mock` - For mocking Azure clients and responses
- `pytest` - Test framework
- `pytest-asyncio` - For async test support
- `pytest-mock` - Enhanced mocking capabilities
- `pytest-cov` - Coverage reporting

## Mock Strategy

Tests use comprehensive mocking to:
- Avoid real Azure API calls during testing
- Test error conditions safely
- Ensure fast test execution
- Enable testing without credentials

## Coverage Report

Generate coverage reports:

```bash
python run_tests.py --coverage
```

This creates:
- Terminal coverage summary
- HTML coverage report in `htmlcov/`

## CI/CD Integration

The test suite is designed for CI/CD integration:

```bash
# Simple CI command
uv run pytest test_face_recognition.py --tb=short

# With coverage for CI
uv run pytest test_face_recognition.py --cov=. --cov-report=xml
```

## Test Markers

Tests are organized with markers:

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests  
- `@pytest.mark.slow` - Slower running tests
- `@pytest.mark.azure` - Tests requiring Azure credentials

## Adding New Tests

When adding new functionality:

1. Add unit tests for individual methods
2. Add integration tests for complete workflows
3. Add error handling tests
4. Update this documentation

## Example Test Run

```bash
$ python run_tests.py --verbose

🧪 Face Recognition System Test Runner
==================================================
Running: uv run pytest test_face_recognition.py -vv

========================= test session starts =========================
collected 15 items

test_face_recognition.py::TestFaceRecognitionManager::test_initialization PASSED
test_face_recognition.py::TestFaceRecognitionManager::test_create_known_person PASSED
test_face_recognition.py::TestFaceRecognitionManager::test_add_face_to_known_person PASSED
test_face_recognition.py::TestFaceRecognitionManager::test_update_unknown_person_with_real_name PASSED
test_face_recognition.py::TestFaceRecognitionManager::test_process_frame PASSED
test_face_recognition.py::TestFaceRecognitionManager::test_get_group_statistics PASSED
test_face_recognition.py::TestFaceRecognitionManager::test_get_unknown_persons_list PASSED
test_face_recognition.py::TestFrameProcessingResult::test_frame_processing_result_creation PASSED
test_face_recognition.py::TestDetectedFace::test_detected_face_creation PASSED
test_face_recognition.py::TestDetectedFace::test_detected_face_unknown PASSED
test_face_recognition.py::TestIntegration::test_full_workflow_simulation PASSED
test_face_recognition.py::TestErrorHandling::test_invalid_credentials PASSED
test_face_recognition.py::TestErrorHandling::test_none_image_handling PASSED
test_face_recognition.py::TestErrorHandling::test_empty_person_id PASSED

========================= 15 tests passed =========================

✅ All tests passed!
```
