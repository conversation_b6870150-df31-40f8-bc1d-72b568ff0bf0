# coding: utf-8

"""
Tests Package

Comprehensive test suite for the face recognition system.

Test Structure:
- test_face_recognition.py: Main test file with unit and integration tests
- run_tests.py: Test runner script with various options
- TESTING.md: Complete testing documentation

Test Categories:
- Unit tests: Individual component testing
- Integration tests: End-to-end workflow testing
- Error handling tests: Edge cases and failure scenarios

Usage:
    python tests/run_tests.py
    pytest tests/test_face_recognition.py -v
"""

__version__ = "1.0.0"
