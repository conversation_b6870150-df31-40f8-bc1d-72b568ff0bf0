[pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
markers =
    unit: Unit tests for individual components
    integration: Integration tests for complete workflows
    slow: Tests that take longer to run
    azure: Tests that require Azure Face API credentials
