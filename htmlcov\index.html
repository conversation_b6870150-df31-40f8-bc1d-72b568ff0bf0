<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">32%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 03:44 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6___init___py.html">face_recognition\__init__.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html">face_recognition\base_group_manager.py</a></td>
                <td>200</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="61 200">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html">face_recognition\face_group_manager.py</a></td>
                <td>240</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="89 240">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html">face_recognition\face_recognition_manager.py</a></td>
                <td>77</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="75 77">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html">face_recognition\known_face_group.py</a></td>
                <td>149</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="21 149">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html">face_recognition\unknown_face_group.py</a></td>
                <td>194</td>
                <td>171</td>
                <td>0</td>
                <td class="right" data-ratio="23 194">12%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>868</td>
                <td>591</td>
                <td>0</td>
                <td class="right" data-ratio="277 868">32%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 03:44 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_3b431d04926d1de6_unknown_face_group_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_3b431d04926d1de6___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
