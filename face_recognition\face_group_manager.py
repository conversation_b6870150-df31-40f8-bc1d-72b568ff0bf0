# coding: utf-8

"""
FILE: face_group_manager.py

DESCRIPTION:
    Face group manager that coordinates between known and unknown face groups.
    Handles frame processing, dual identification, and training coordination.

FEATURES:
    - Coordinates between KnownFaceGroup and UnknownFaceGroup
    - Dual identification calls (known + unknown)
    - Frame processing with face detection
    - Training coordination across all groups
    - Statistics aggregation
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import time

from azure.ai.vision.face import FaceClient
from azure.ai.vision.face.models import FaceDetectionModel, FaceRecognitionModel, FaceAttributeType
from azure.core.credentials import AzureKeyCredential

from .known_face_group import KnownFaceGroup
from .unknown_face_group import UnknownFaceGroup


@dataclass
class DetectedFace:
	"""Data class for detected face information"""
	face_id: str
	person_id: str | None
	tag_text: str
	confidence: float
	is_known: bool
	source_group: str
	bounding_box: tuple[int, int, int, int]
	cropped_image: np.ndarray | None = None


@dataclass
class FrameProcessingResult:
	"""Data class for frame processing results"""
	detected_faces: List[DetectedFace]
	total_faces: int
	known_faces: int
	unknown_faces: int
	processing_time: float
	annotated_frame: np.ndarray


class FaceGroupManager:
	"""Coordinates face group operations and frame processing"""
	
	def __init__(
		self,
		endpoint: str,
		api_key: str,
		group_id: str,
		group_name: str,
		auto_train_unknown: bool = True,
		max_faces_per_person: int = 10,
		unknown_confidence_threshold: float = 0.8,
		face_similarity_threshold: float = 0.8,
		save_known_images: bool = False,
		logger: Optional[Any] = None
	):
		"""Initialize face group manager"""
		self.endpoint = endpoint
		self.api_key = api_key
		self.group_id = group_id
		self.group_name = group_name
		self.auto_train_unknown = auto_train_unknown
		self.max_faces_per_person = max_faces_per_person
		self.unknown_confidence_threshold = unknown_confidence_threshold
		self.face_similarity_threshold = face_similarity_threshold
		self.save_known_images = save_known_images
		
		# Setup logger
		self.logger = logger or self._setup_logger()
		
		# Initialize Azure Face API client
		credential = AzureKeyCredential(api_key)
		self.face_client = FaceClient(endpoint, credential)
		
		# Initialize group managers
		self.known_groups = KnownFaceGroup(
			endpoint, api_key, group_id, group_name,
			max_faces_per_person, face_similarity_threshold, save_known_images, logger
		)
		
		self.unknown_groups = UnknownFaceGroup(
			endpoint, api_key, group_id, group_name,
			max_faces_per_person, face_similarity_threshold,
			unknown_confidence_threshold, auto_train_unknown, logger
		)
		
		self.logger.info(f"Initialized FaceGroupManager for: {group_name}")
	
	def _setup_logger(self):
		"""Setup logger for the group manager"""
		import logging
		logger = logging.getLogger(f"{self.__class__.__name__}")
		if not logger.handlers:
			handler = logging.StreamHandler()
			formatter = logging.Formatter(
				'%(asctime)s %(levelname)s %(message)s',
				datefmt='%Y-%m-%d %H:%M:%S'
			)
			handler.setFormatter(formatter)
			logger.addHandler(handler)
			logger.setLevel(logging.INFO)
		return logger
	
	def process_frame(
		self,
		frame: np.ndarray,
		confidence_threshold: float = 0.6,
		return_known_crops: bool = True,
		auto_train_unknown: bool = None
	) -> FrameProcessingResult:
		"""Process video frame and identify faces"""
		start_time = time.time()
		
		if auto_train_unknown is None:
			auto_train_unknown = self.auto_train_unknown
		
		try:
			# Detect faces in frame
			detected_faces_api = self._detect_faces_in_frame(frame)
			
			if not detected_faces_api:
				processing_time = time.time() - start_time
				return FrameProcessingResult(
					detected_faces=[],
					total_faces=0,
					known_faces=0,
					unknown_faces=0,
					processing_time=processing_time,
					annotated_frame=frame.copy()
				)
			
			# Extract face IDs for identification
			face_ids = [face.face_id for face in detected_faces_api]
			
			# Dual identification: known and unknown groups
			known_results, unknown_results = self._dual_identify(face_ids, confidence_threshold)
			
			# Process identification results
			detected_faces = self._process_identification_results(
				frame, detected_faces_api, known_results, unknown_results,
				confidence_threshold, return_known_crops, auto_train_unknown
			)
			
			# Create annotated frame
			annotated_frame = self._annotate_frame(frame, detected_faces)
			
			# Calculate statistics
			known_count = sum(1 for face in detected_faces if face.is_known and face.source_group == "known")
			unknown_count = len(detected_faces) - known_count
			
			processing_time = time.time() - start_time
			
			return FrameProcessingResult(
				detected_faces=detected_faces,
				total_faces=len(detected_faces),
				known_faces=known_count,
				unknown_faces=unknown_count,
				processing_time=processing_time,
				annotated_frame=annotated_frame
			)
			
		except Exception as e:
			self.logger.error(f"Error processing frame: {e}")
			processing_time = time.time() - start_time
			return FrameProcessingResult(
				detected_faces=[],
				total_faces=0,
				known_faces=0,
				unknown_faces=0,
				processing_time=processing_time,
				annotated_frame=frame.copy()
			)
	
	def _detect_faces_in_frame(self, frame: np.ndarray) -> List[Any]:
		"""Detect faces in frame using Azure Face API"""
		try:
			# Convert frame to bytes
			image_bytes = self._image_to_bytes(frame)
			
			# Detect faces
			detected_faces = self.face_client.detect(
				image_bytes,
				detection_model=FaceDetectionModel.DETECTION03,
				recognition_model=FaceRecognitionModel.RECOGNITION04,
				return_face_id=True,
				return_face_attributes=[
					FaceAttributeType.BLUR,
					FaceAttributeType.EXPOSURE,
					FaceAttributeType.NOISE
				]
			)
			
			self.logger.debug(f"Detected {len(detected_faces)} faces in frame")
			return detected_faces
			
		except Exception as e:
			self.logger.error(f"Error detecting faces: {e}")
			return []
	
	def _dual_identify(self, face_ids: List[str], confidence_threshold: float) -> Tuple[List[Any], List[Any]]:
		"""Perform dual identification against known and unknown groups"""
		try:
			# Set identification in progress for smart training coordination
			self.known_groups.set_identification_in_progress(True)
			self.unknown_groups.set_identification_in_progress(True)

			# Identify against known groups
			known_results = self.known_groups.identify_faces(face_ids, confidence_threshold)

			# Identify against unknown groups
			unknown_results = self.unknown_groups.identify_faces(face_ids, confidence_threshold)

			return known_results, unknown_results

		except Exception as e:
			self.logger.error(f"Error in dual identification: {e}")
			return [], []
		finally:
			# Clear identification in progress flag
			self.known_groups.set_identification_in_progress(False)
			self.unknown_groups.set_identification_in_progress(False)
	
	def _image_to_bytes(self, image: np.ndarray) -> bytes:
		"""Convert numpy image to bytes"""
		try:
			# Convert BGR to RGB if needed
			if len(image.shape) == 3 and image.shape[2] == 3:
				image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
			else:
				image_rgb = image
			
			# Encode as JPEG
			from PIL import Image
			from io import BytesIO
			pil_image = Image.fromarray(image_rgb)
			
			# Save to bytes
			buffer = BytesIO()
			pil_image.save(buffer, format='JPEG', quality=95)
			return buffer.getvalue()
			
		except Exception as e:
			self.logger.error(f"Error converting image to bytes: {e}")
			raise

	def _process_identification_results(
		self,
		frame: np.ndarray,
		detected_faces_api: List[Any],
		known_results: List[Any],
		unknown_results: List[Any],
		confidence_threshold: float,
		return_known_crops: bool,
		auto_train_unknown: bool
	) -> List[DetectedFace]:
		"""Process identification results and create DetectedFace objects"""
		detected_faces = []

		for i, detected_face in enumerate(detected_faces_api):
			person_id = None
			person_name = None
			confidence = 0.0
			is_known = False
			source_group = None

			# Check known group first (higher priority)
			if i < len(known_results) and known_results[i].candidates:
				best_candidate = known_results[i].candidates[0]
				if best_candidate.confidence >= confidence_threshold:
					# Get person details from known group
					try:
						person = self.known_groups.face_admin_client.large_person_group.get_person(
							self.known_groups.get_active_group_id(),
							best_candidate.person_id
						)
						person_id = best_candidate.person_id
						person_name = person.name
						confidence = best_candidate.confidence
						is_known = True
						source_group = "known"

						# Auto-add face to known person if conditions are met
						if confidence >= 0.8 and len(person.persisted_face_ids or []) < self.max_faces_per_person:
							self.known_groups.add_face_to_known_person_if_needed(
								person_id, frame, detected_face, confidence
							)

					except Exception as e:
						self.logger.warning(f"Failed to get known person details: {e}")

			# If not found in known group, check unknown group
			if not is_known and i < len(unknown_results) and unknown_results[i].candidates:
				best_candidate = unknown_results[i].candidates[0]
				if best_candidate.confidence >= confidence_threshold:
					# Get person details from unknown group
					try:
						person = self.unknown_groups.face_admin_client.large_person_group.get_person(
							self.unknown_groups.get_active_group_id(),
							best_candidate.person_id
						)
						person_id = best_candidate.person_id
						person_name = person.name
						confidence = best_candidate.confidence
						is_known = True  # Known in unknown group
						source_group = "unknown"
					except Exception as e:
						self.logger.warning(f"Failed to get unknown person details: {e}")

			if not is_known:
				# Unknown face - create new unknown person if auto-training enabled
				if auto_train_unknown:
					face_image = self._crop_face_from_frame(frame, detected_face)
					if face_image is not None:
						result = self.unknown_groups.create_unknown_person(face_image)
						if result:
							person_id, person_name = result
							is_known = True
							source_group = "unknown"
							confidence = 0.0

			# Set default name if still unknown
			if not person_name:
				person_name = "Unknown"

			# Get bounding box
			rect = detected_face.face_rectangle
			bounding_box = (rect.left, rect.top, rect.width, rect.height)

			# Crop face if needed
			cropped_image = None
			if (is_known and source_group == "known" and return_known_crops) or \
			   (is_known and source_group == "unknown"):
				cropped_image = self._crop_face_from_frame(frame, detected_face)

			detected_faces.append(DetectedFace(
				face_id=detected_face.face_id,
				person_id=person_id,
				tag_text=person_name,
				confidence=confidence,
				is_known=is_known,
				source_group=source_group or "none",
				bounding_box=bounding_box,
				cropped_image=cropped_image
			))

		return detected_faces

	def _crop_face_from_frame(self, frame: np.ndarray, detected_face) -> np.ndarray | None:
		"""Crop face from frame using detection rectangle"""
		try:
			if not detected_face or not detected_face.face_rectangle:
				return None

			rect = detected_face.face_rectangle
			height, width = frame.shape[:2]

			# Calculate crop coordinates with padding
			padding = 20
			left = max(0, rect.left - padding)
			top = max(0, rect.top - padding)
			right = min(width, rect.left + rect.width + padding)
			bottom = min(height, rect.top + rect.height + padding)

			# Crop face
			face_crop = frame[top:bottom, left:right]

			if face_crop.size == 0:
				return None

			return face_crop

		except Exception as e:
			self.logger.error(f"Error cropping face: {e}")
			return None

	def _annotate_frame(self, frame: np.ndarray, detected_faces: List[DetectedFace]) -> np.ndarray:
		"""Annotate frame with face detection results"""
		annotated_frame = frame.copy()

		for face in detected_faces:
			left, top, width, height = face.bounding_box
			right = left + width
			bottom = top + height

			# Choose color based on face type
			if face.source_group == "known":
				color = (0, 255, 0)  # Green for known
			elif face.source_group == "unknown":
				color = (0, 165, 255)  # Orange for unknown
			else:
				color = (0, 0, 255)  # Red for unidentified

			# Draw rectangle
			cv2.rectangle(annotated_frame, (left, top), (right, bottom), color, 2)

			# Prepare label
			if face.confidence > 0:
				label = f"{face.tag_text} ({face.confidence:.2f})"
			else:
				label = face.tag_text

			# Draw label background
			label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
			cv2.rectangle(
				annotated_frame,
				(left, top - label_size[1] - 10),
				(left + label_size[0], top),
				color,
				-1
			)

			# Draw label text
			cv2.putText(
				annotated_frame,
				label,
				(left, top - 5),
				cv2.FONT_HERSHEY_SIMPLEX,
				0.6,
				(255, 255, 255),
				2
			)

		return annotated_frame

	def get_group_statistics(self) -> Dict[str, Any]:
		"""Get comprehensive statistics for all groups"""
		try:
			known_stats = self.known_groups.get_group_statistics()
			unknown_stats = self.unknown_groups.get_group_statistics()

			# Combine statistics
			combined_stats = {
				**known_stats,
				**unknown_stats,
				'settings': {
					'auto_train_unknown': self.auto_train_unknown,
					'max_faces_per_person': self.max_faces_per_person,
					'unknown_confidence_threshold': self.unknown_confidence_threshold,
					'face_similarity_threshold': self.face_similarity_threshold
				}
			}

			return combined_stats

		except Exception as e:
			self.logger.error(f"Error getting group statistics: {e}")
			return {}

	def get_training_status(self) -> Dict[str, Any]:
		"""Get training status for all groups"""
		try:
			known_training = self.known_groups.get_training_status()
			unknown_training = self.unknown_groups.get_training_status()

			return {
				**known_training,
				**unknown_training
			}

		except Exception as e:
			self.logger.error(f"Error getting training status: {e}")
			return {}

	# Delegation methods for known groups
	def create_known_person(self, name: str, user_data: str = "") -> str | None:
		"""Create a person in known groups"""
		return self.known_groups.create_known_person(name, user_data)

	def add_face_to_known_person(self, person_id: str, face_image: np.ndarray, user_data: str = "") -> str | None:
		"""Add face to known person"""
		return self.known_groups.add_face_to_known_person(person_id, face_image, user_data)

	def get_known_persons(self) -> List[Any]:
		"""Get all known persons"""
		return self.known_groups.get_known_persons()

	# Delegation methods for unknown groups
	def update_unknown_person_with_real_name(self, person_label: str, real_name: str, user_data: str = "") -> bool:
		"""Update unknown person with real name"""
		return self.unknown_groups.update_unknown_person_with_real_name(person_label, real_name, user_data)

	def transfer_unknown_to_known_person(self, unknown_person_label: str, real_name: str, user_data: str = "") -> str | None:
		"""Transfer unknown person to known group with all their saved images"""
		try:
			# Find the unknown person by label
			unknown_person_id = None
			unknown_persons = self.unknown_groups.get_unknown_persons_list()

			for person in unknown_persons:
				if person.get('name') == unknown_person_label:
					unknown_person_id = person.get('person_id')
					break

			if not unknown_person_id:
				self.logger.error(f"Unknown person not found: {unknown_person_label}")
				return None

			# Create new known person
			known_person_id = self.known_groups.create_known_person(real_name, user_data)
			if not known_person_id:
				self.logger.error(f"Failed to create known person: {real_name}")
				return None

			# Get saved images for the unknown person
			saved_images = self.unknown_groups.get_unknown_person_images(unknown_person_id)

			if saved_images:
				# Transfer images from unknown to known folder
				transfer_success = self.unknown_groups.transfer_unknown_to_known(
					unknown_person_id, known_person_id, real_name, user_data
				)

				if not transfer_success:
					self.logger.error(f"Failed to transfer images for person: {unknown_person_label}")
					return None

				# Add all saved images to the known person when training is available
				self._add_saved_images_to_known_person(saved_images, known_person_id, real_name, user_data)

			# Remove unknown person from groups
			self._remove_unknown_person_from_groups(unknown_person_id)

			self.logger.info(f"Successfully transferred {unknown_person_label} to known person {real_name} (ID: {known_person_id})")
			return known_person_id

		except Exception as e:
			self.logger.error(f"Error transferring unknown to known person: {e}")
			return None

	def get_unknown_persons_list(self) -> list[dict]:
		"""Get list of all unknown persons"""
		return self.unknown_groups.get_unknown_persons_list()

	def _add_saved_images_to_known_person(self, saved_images: List[str], known_person_id: str,
										  real_name: str, user_data: str):
		"""Add saved images to known person when training is available"""
		try:
			import cv2

			for image_path in saved_images:
				try:
					# Load image
					face_image = cv2.imread(image_path)
					if face_image is not None:
						# Add face to known person (this will trigger training when ready)
						face_id = self.known_groups.add_face_to_known_person_if_needed(
							known_person_id, face_image, None, 1.0  # High confidence for saved images
						)
						if face_id:
							# Save known face image (only when training)
							self.known_groups.save_known_face_image(
								face_image, known_person_id, face_id, real_name, user_data
							)
				except Exception as e:
					self.logger.error(f"Error adding saved image {image_path}: {e}")

		except Exception as e:
			self.logger.error(f"Error adding saved images to known person: {e}")

	def _remove_unknown_person_from_groups(self, person_id: str):
		"""Remove person from both unknown groups"""
		try:
			# Try to remove from both primary and secondary unknown groups
			primary_group_id = self.unknown_groups.primary_group_id
			secondary_group_id = self.unknown_groups.secondary_group_id

			for group_id in [primary_group_id, secondary_group_id]:
				try:
					self.face_admin_client.large_person_group.delete_person(group_id, person_id)
					self.logger.info(f"Removed person {person_id} from group {group_id}")
				except Exception as e:
					# Person might not exist in this group, which is fine
					self.logger.debug(f"Person {person_id} not found in group {group_id}: {e}")

		except Exception as e:
			self.logger.error(f"Error removing unknown person from groups: {e}")

	# Group management methods
	def get_active_known_group_id(self) -> str:
		"""Get active known group ID"""
		return self.known_groups.get_active_group_id()

	def get_active_unknown_group_id(self) -> str:
		"""Get active unknown group ID"""
		return self.unknown_groups.get_active_group_id()

	def cleanup_all_groups(self) -> bool:
		"""Clean up all groups"""
		try:
			known_success = self.known_groups.cleanup_known_groups()
			unknown_success = self.unknown_groups.cleanup_unknown_groups()

			return known_success and unknown_success

		except Exception as e:
			self.logger.error(f"Error cleaning up all groups: {e}")
			return False
