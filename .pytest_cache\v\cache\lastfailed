{"tests/test_face_recognition.py::TestIntegration": true, "tests/test_face_recognition.py::TestErrorHandling": true, "tests/test_face_recognition.py::TestFaceRecognitionManager::test_initialization": true, "tests/test_face_recognition.py::TestIntegration::test_full_workflow_simulation": true, "tests/test_face_recognition.py::TestErrorHandling::test_invalid_credentials": true, "tests/test_face_recognition.py::TestErrorHandling::test_none_image_handling": true, "tests/test_face_recognition.py::TestErrorHandling::test_empty_person_id": true}