# coding: utf-8

"""
FILE: face_recognition_manager.py

DESCRIPTION:
    Clean face recognition manager using modular architecture.
    Uses FaceGroupManager internally for all operations.

FEATURES:
    - Clean, simplified public API
    - Modular architecture with separate group classes
    - Enhanced functionality with auto-face addition and timestamp tracking
    - Professional unknown person management
    - Update unknown persons with real names and designations
"""

import logging
import numpy as np
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from .face_group_manager import FaceGroupManager, FrameProcessingResult, DetectedFace


class FaceRecognitionManager:
	"""Simplified face recognition manager with modular architecture"""
	
	def __init__(
		self,
		endpoint: str,
		api_key: str,
		group_id: str,
		group_name: str,
		auto_train_unknown: bool = True,
		max_faces_per_person: int = 10,
		unknown_confidence_threshold: float = 0.8,
		face_similarity_threshold: float = 0.8,
		save_known_images: bool = False,
		logger: Optional[logging.Logger] = None
	):
		"""
		Initialize Face Recognition Manager

		Args:
			endpoint: Azure Face API endpoint
			api_key: Azure Face API key
			group_id: Base group ID for all groups
			group_name: Base group name
			auto_train_unknown: Enable automatic training for unknown faces
			max_faces_per_person: Maximum faces per person (enables auto-addition)
			unknown_confidence_threshold: Confidence threshold for unknown faces
			face_similarity_threshold: Similarity threshold for face matching
			save_known_images: Whether to save known face images during training
			logger: Optional logger instance
		"""
		self.endpoint = endpoint
		self.api_key = api_key
		self.group_id = group_id
		self.group_name = group_name
		self.auto_train_unknown = auto_train_unknown
		self.max_faces_per_person = max_faces_per_person
		self.unknown_confidence_threshold = unknown_confidence_threshold
		self.face_similarity_threshold = face_similarity_threshold
		self.save_known_images = save_known_images
		
		# Setup logger
		self.logger = logger or self._setup_logger()
		
		# Initialize the group manager (does all the heavy lifting)
		self.group_manager = FaceGroupManager(
			endpoint=endpoint,
			api_key=api_key,
			group_id=group_id,
			group_name=group_name,
			auto_train_unknown=auto_train_unknown,
			max_faces_per_person=max_faces_per_person,
			unknown_confidence_threshold=unknown_confidence_threshold,
			face_similarity_threshold=face_similarity_threshold,
			save_known_images=save_known_images,
			logger=logger
		)
		
		self.logger.info(f"Initialized FaceRecognitionManager: {group_name}")
		self.logger.info(f"✅ Auto-face addition: {'Enabled' if max_faces_per_person > 1 else 'Disabled'}")
		self.logger.info(f"✅ Timestamp tracking: Enabled")
		self.logger.info(f"✅ Unknown person management: Enabled")
		self.logger.info(f"✅ Quad-group architecture: Active")
		self.logger.info(f"✅ Modular architecture: Clean & maintainable")
	
	def _setup_logger(self) -> logging.Logger:
		"""Setup logger for the manager"""
		logger = logging.getLogger(f"{self.__class__.__name__}")
		if not logger.handlers:
			handler = logging.StreamHandler()
			formatter = logging.Formatter(
				'%(asctime)s %(levelname)s %(message)s',
				datefmt='%Y-%m-%d %H:%M:%S'
			)
			handler.setFormatter(formatter)
			logger.addHandler(handler)
			logger.setLevel(logging.INFO)
		return logger
	
	# Main frame processing method
	def process_frame(
		self,
		frame: np.ndarray,
		confidence_threshold: float = 0.6,
		return_known_crops: bool = True,
		auto_train_unknown: bool = None
	) -> FrameProcessingResult:
		"""
		Process video frame and identify faces
		
		Args:
			frame: Input video frame
			confidence_threshold: Minimum confidence for face identification
			return_known_crops: Whether to return cropped images for known faces
			auto_train_unknown: Override auto-training setting for this frame
			
		Returns:
			FrameProcessingResult with detected faces and statistics
		"""
		return self.group_manager.process_frame(
			frame, confidence_threshold, return_known_crops, auto_train_unknown
		)
	
	# Known person management
	def create_known_person(self, name: str, user_data: str = "") -> str | None:
		"""
		Create a new known person

		Args:
			name: Person's name
			user_data: Person's designation or short summary (e.g., "Manager", "Developer")

		Returns:
			Person ID if successful, None otherwise
		"""
		return self.group_manager.create_known_person(name, user_data)
	
	def add_face_to_known_person(self, person_id: str, face_image: np.ndarray, user_data: str = "") -> str | None:
		"""
		Add face image to existing known person

		Args:
			person_id: ID of the person
			face_image: Face image (numpy array) to add to the person
			user_data: Additional context (e.g., "Photo from meeting", "ID card photo")

		Returns:
			Face ID if successful, None otherwise
		"""
		return self.group_manager.add_face_to_known_person(person_id, face_image, user_data)
	
	def get_known_persons(self) -> List[Any]:
		"""
		Get all known persons from both known groups
		
		Returns:
			List of person objects with group information
		"""
		return self.group_manager.get_known_persons()
	
	# Unknown person management
	def update_unknown_person_with_real_name(self, person_label: str, real_name: str, user_data: str = "") -> bool:
		"""
		Update an unknown person (person_X) with real name and details

		Args:
			person_label: Current auto-generated label (e.g., "person_1", "person_2")
			real_name: Real name to update to (e.g., "John Smith")
			user_data: Person's designation or short summary (e.g., "Manager", "Visitor", "Security Guard")

		Returns:
			True if successful, False otherwise
		"""
		return self.group_manager.update_unknown_person_with_real_name(person_label, real_name, user_data)
	
	def get_unknown_persons_list(self) -> list[dict]:
		"""
		Get list of all unknown persons with metadata
		
		Returns:
			List of dictionaries with person information
		"""
		return self.group_manager.get_unknown_persons_list()
	
	# Statistics and monitoring
	def get_group_statistics(self) -> Dict[str, Any]:
		"""
		Get comprehensive statistics for all groups
		
		Returns:
			Dictionary with statistics for all 4 groups
		"""
		return self.group_manager.get_group_statistics()
	
	def get_training_status(self) -> Dict[str, Any]:
		"""
		Get training status for all groups
		
		Returns:
			Dictionary with training status for all groups
		"""
		return self.group_manager.get_training_status()
	
	# Group information
	def get_active_known_group_id(self) -> str:
		"""Get the currently active known group ID"""
		return self.group_manager.get_active_known_group_id()
	
	def get_active_unknown_group_id(self) -> str:
		"""Get the currently active unknown group ID"""
		return self.group_manager.get_active_unknown_group_id()
	
	# Utility methods
	def cleanup_all_groups(self) -> bool:
		"""
		Clean up all groups (delete all persons)
		
		Returns:
			True if successful, False otherwise
		"""
		return self.group_manager.cleanup_all_groups()
	
	def save_cropped_faces(self, detected_faces: List[DetectedFace], output_dir: str = "cropped_faces") -> List[str]:
		"""
		Save cropped face images to directory
		
		Args:
			detected_faces: List of detected faces with cropped images
			output_dir: Output directory for saved images
			
		Returns:
			List of saved file paths
		"""
		import os
		import cv2
		from datetime import datetime
		
		os.makedirs(output_dir, exist_ok=True)
		saved_files = []
		
		timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
		
		for i, face in enumerate(detected_faces):
			if face.cropped_image is not None:
				# Create filename
				safe_name = face.tag_text.replace(" ", "_").replace("/", "_")
				filename = f"{timestamp}_{i:02d}_{safe_name}_{face.confidence:.2f}.jpg"
				filepath = os.path.join(output_dir, filename)
				
				# Save image
				try:
					cv2.imwrite(filepath, face.cropped_image)
					saved_files.append(filepath)
					self.logger.info(f"Saved cropped face: {filepath}")
				except Exception as e:
					self.logger.error(f"Error saving cropped face {filepath}: {e}")
		
		return saved_files
	
	def __str__(self) -> str:
		"""String representation of the manager"""
		return f"FaceRecognitionManager(group_id='{self.group_id}', name='{self.group_name}')"
	
	def __repr__(self) -> str:
		"""Detailed representation of the manager"""
		return (f"FaceRecognitionManager(group_id='{self.group_id}', "
				f"auto_train={self.auto_train_unknown}, "
				f"max_faces={self.max_faces_per_person})")
