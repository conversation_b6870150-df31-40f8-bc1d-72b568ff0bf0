<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">32%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 03:44 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6___init___py.html">face_recognition\__init__.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t31">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html#t31"><data value='BaseGroupManager'>BaseGroupManager</data></a></td>
                <td>170</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="31 170">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html">face_recognition\base_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_base_group_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t34">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t34"><data value='DetectedFace'>DetectedFace</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t47">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t47"><data value='FrameProcessingResult'>FrameProcessingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t57">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html#t57"><data value='FaceGroupManager'>FaceGroupManager</data></a></td>
                <td>191</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="40 191">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html">face_recognition\face_group_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_group_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t26">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html#t26"><data value='FaceRecognitionManager'>FaceRecognitionManager</data></a></td>
                <td>55</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="53 55">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html">face_recognition\face_recognition_manager.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_face_recognition_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t23">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html#t23"><data value='KnownFaceGroup'>KnownFaceGroup</data></a></td>
                <td>128</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="0 128">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html">face_recognition\known_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_known_face_group_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t24">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html#t24"><data value='UnknownFaceGroup'>UnknownFaceGroup</data></a></td>
                <td>171</td>
                <td>171</td>
                <td>0</td>
                <td class="right" data-ratio="0 171">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html">face_recognition\unknown_face_group.py</a></td>
                <td class="name left"><a href="z_3b431d04926d1de6_unknown_face_group_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>868</td>
                <td>591</td>
                <td>0</td>
                <td class="right" data-ratio="277 868">32%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 03:44 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
